# ============================================================================
# TRADING AUTOMATION - SECURE ENV CONFIG
# Last Updated: 2025-09-03
# ============================================================================
# IMPORTANT: Replace placeholder values before running in production
# ============================================================================

# ============================================================================
# CORE APP CONFIG
# ============================================================================
ENVIRONMENT=production
APP_NAME=TradingView Automation Bot
APP_VERSION=2.0.0
DEBUG=false
LOG_LEVEL=INFO
TRADINGBOT_ENV=production

# ============================================================================
# FRONTEND / API SETTINGS
# ============================================================================
FRONTEND_URL=http://localhost:3000
ALLOWED_ORIGIN=http://localhost:3000

API_HOST=0.0.0.0
API_PORT=8000
API_RATE_LIMIT=100
API_RATE_WINDOW=60

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================
USE_SUPABASE=true
# ======================================================================
# DATABASE CONFIGURATION
# ======================================================================
# Primary Database (Supabase)
USE_SUPABASE=true
DATABASE_URL=postgresql+asyncpg://postgres:<EMAIL>:5432/postgres
# Direct Supabase database URL
SUPABASE_DB_URL=postgresql://postgres:<EMAIL>:5432/postgres
# Fallback IP address for Supabase database (used when DNS resolution fails)
SUPABASE_FALLBACK_IP=***********
SUPABASE_URL=https://sgxjackuhalscowqrulv.supabase.co
SUPABASE_KEY=********************************************
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNneGphY2t1aGFsc2Nvd3FydWx2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5NjExNDMsImV4cCI6MjA2NTUzNzE0M30.-gBZv9TWmb4nkyqhpaZzRtg6BY1lPArnz7QBOehh8sE
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNneGphY2t1aGFsc2Nvd3FydWx2Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTk2MTE0MywiZXhwIjoyMDY1NTM3MTQzfQ.4Nz4q6HN3XGgd23l_xCSkZWD1qDh3U0UWY4m-aDbqrA
 
 
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_ECHO=false

# ============================================================================
# REDIS CONFIGURATION
# ============================================================================
REDIS_ENABLED=true
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=123SECURE_REDIS_PASSWORD!@#
REDIS_POOL_SIZE=10
REDIS_MAX_CONNECTIONS=20

# ============================================================================
# SECURITY SETTINGS
# ============================================================================

JWT_SECRET=vLzXgYq8bNfP6hJ2kM5sR9uV0wZ4yA1cE7fG3hK5jP3sR6uV0xY4zC1bE7fG2hK5jP3sR6uV0wZ4yA1c
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS
CORS_ALLOW_CREDENTIALS=true
CORS_MAX_AGE=600

# ============================================================================
# DISCORD BOT CONFIG
# ============================================================================
DISCORD_ENABLED=true
DISCORD_BOT_TOKEN=MTQwNDUwNjk2MTc3NjQ4MDMxNg.Gq-zfl.vSZgjzMt4T_MyObj-a1MNhXSyi7yGC4p1z3o7o
DISCORD_GUILD_ID=1304548446090301440
DISCORD_AI_CHANNEL_ID=1403195431730810932
DISCORD_WEBHOOK_URL=

# ============================================================================
# AI / LLM CONFIG
# ============================================================================
OPENROUTER_ENABLED=true
OPENROUTER_API_KEY=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
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Default models
OPENROUTER_GENERAL_MODEL=openrouter/sonoma-sky-alpha
OPENROUTER_GENERAL_MODEL_MAX_TOKENS=2000
OPENROUTER_GENERAL_MODEL_TEMPERATURE=0.7

MODEL_GLOBAL_FALLBACK=moonshotai/kimi-k2:free
FREE_MODELS=z-ai/glm-4.5-air:free,deepseek/deepseek-r1:free,moonshotai/kimi-k2:free,qwen/qwen2.5-7b-instruct:free

# Additional model configuration for legacy compatibility
MODEL_LLM=moonshotai/kimi-k2:free
MODEL_QUICK=moonshotai/kimi-k2:free
MODEL_ANALYSIS=moonshotai/kimi-k2:free
MODEL_HEAVY=moonshotai/kimi-k2:free

# ============================================================================
# AI MODEL CONFIGURATION
# ============================================================================
# AI Model defaults
AI_DEFAULT_TEMPERATURE=0.7
AI_DEFAULT_MAX_TOKENS=2000
AI_DEFAULT_TIMEOUT_MS=30000
AI_DEFAULT_COST=0.001
AI_DEFAULT_ACCURACY=0.8
AI_DEFAULT_RESPONSE_TIME=2000

# GPT-4o Mini Configuration
GPT_4O_MINI_MODEL_ID=gpt-4o-mini
GPT_4O_MINI_MAX_TOKENS=16384
GPT_4O_MINI_COST=0.00015
GPT_4O_MINI_RESPONSE_TIME=2000
GPT_4O_MINI_ACCURACY=0.85
GPT_4O_MINI_ENABLED=true

# GPT-4o Configuration
GPT_4O_MODEL_ID=gpt-4o
GPT_4O_MAX_TOKENS=128000
GPT_4O_COST=0.005
GPT_4O_RESPONSE_TIME=3000
GPT_4O_ACCURACY=0.92
GPT_4O_ENABLED=true



# Claude 3.5 Sonnet Configuration
CLAUDE_3_5_SONNET_MODEL_ID=claude-3-5-sonnet-20241022
CLAUDE_3_5_SONNET_MAX_TOKENS=200000
CLAUDE_3_5_SONNET_COST=0.003
CLAUDE_3_5_SONNET_RESPONSE_TIME=2500
CLAUDE_3_5_SONNET_ACCURACY=0.90
CLAUDE_3_5_SONNET_ENABLED=true

# Mixtral 8x7B Configuration
MIXTRAL_8X7B_MODEL_ID=mixtral-8x7b-instruct
MIXTRAL_8X7B_MAX_TOKENS=32768
MIXTRAL_8X7B_COST=0.0005
MIXTRAL_8X7B_RESPONSE_TIME=2000
MIXTRAL_8X7B_ACCURACY=0.82
MIXTRAL_8X7B_ENABLED=true

# Mixtral 8x7B Configuration
MIXTRAL_8X7B_MODEL_ID=mixtral-8x7b-instruct
MIXTRAL_8X7B_MAX_TOKENS=32768
MIXTRAL_8X7B_COST=0.00014
MIXTRAL_8X7B_RESPONSE_TIME=1500
MIXTRAL_8X7B_ACCURACY=0.78
MIXTRAL_8X7B_ENABLED=true

# Llama 3 8B Configuration
LLAMA_3_8B_MODEL_ID=llama-3-8b-instruct
LLAMA_3_8B_MAX_TOKENS=8192
LLAMA_3_8B_COST=0.00010
LLAMA_3_8B_RESPONSE_TIME=1000
LLAMA_3_8B_ACCURACY=0.75
LLAMA_3_8B_ENABLED=true

# AI Scoring Weights
AI_SCORING_ACCURACY_WEIGHT=0.4
AI_SCORING_COST_WEIGHT=0.25
AI_SCORING_TIME_WEIGHT=0.2
AI_SCORING_CAPACITY_WEIGHT=0.1
AI_SCORING_PREFERENCE_WEIGHT=0.05

# Complexity Multipliers
AI_COMPLEXITY_SIMPLE=1.0
AI_COMPLEXITY_MODERATE=1.5
AI_COMPLEXITY_COMPLEX=2.0
AI_COMPLEXITY_EXPERT=2.5
AI_COMPLEXITY_REAL_TIME=3.0

# Performance Tracking
AI_PERFORMANCE_TRACKING=true
AI_METRICS_RETENTION=30
AI_CIRCUIT_BREAKER_THRESHOLD=5
AI_CIRCUIT_BREAKER_TIMEOUT=300

# AI Pipeline Configuration
ASK_PIPELINE_TIMEOUT=30.0
ASK_PIPELINE_CACHE_TTL=3600
ASK_PIPELINE_MAX_RETRIES=3
ASK_PIPELINE_PARALLEL_EXECUTION=false

# AI Model Selection Preferences
AI_PREFER_COST_EFFICIENCY=false
AI_PREFER_SPEED=false
AI_PREFER_ACCURACY=true
AI_ENABLE_FALLBACK_CHAIN=true
AI_MAX_FALLBACK_ATTEMPTS=3



# ============================================================================
# MARKET DATA PROVIDERS
# ============================================================================
POLYGON_API_KEY=********************************
FINNHUB_API_KEY=d2mjok1r01qog4441lsgd2mjok1r01qog4441lt0
ALPHA_VANTAGE_API_KEY=<ALPHA_KEY>
ALPACA_API_KEY=<ALPACA_KEY>
ALPACA_API_SECRET=<ALPACA_SECRET>
ALPACA_BASE_URL=https://paper-api.alpaca.markets

# Enable/disable
YAHOO_FINANCE_ENABLED=true
POLYGON_ENABLED=true
FINNHUB_ENABLED=true
ALPHA_VANTAGE_ENABLED=false
ALPACA_ENABLED=false

# ============================================================================
# TRADING PARAMETERS
# ============================================================================
RISK_PER_TRADE=0.02
MAX_POSITION_SIZE=0.1
STOP_LOSS_MULTIPLIER=2.0
TAKE_PROFIT_MULTIPLIER=3.0
MAX_OPEN_POSITIONS=5
MINIMUM_VOLUME_THRESHOLD=100000.0
PRICE_CHANGE_THRESHOLD=0.05
SUPPORT_RESISTANCE_PERCENTAGE=0.05

# ============================================================================
# MONITORING & LOGGING
# ============================================================================
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

LOG_FORMAT=json
LOG_FILE_PATH=/app/logs
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5

# ============================================================================
# NGROK CONFIGURATION
# ============================================================================
NGROK_AUTHTOKEN=30I3DmIoKUoLSo1S6s2VR9hJbGT_51zxMZtp5pGKqR5sdGKHT

# ============================================================================
# DEV/TEST OVERRIDES (disable in production)
# ============================================================================
ENABLE_MOCK_DATA=false
ENABLE_DEBUG_LOGGING=true
ENABLE_PERFORMANCE_PROFILING=true
ENABLE_API_RESPONSE_LOGGING=true
