# Core Application Dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.7.1
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0
psycopg2-binary==2.9.7  # PostgreSQL adapter for Supabase
redis==5.0.1
yfinance==0.2.18
pandas==2.1.1
numpy==1.24.3
discord.py==2.3.2  # Discord bot API
langdetect==1.0.9  # Language detection library
SpeechRecognition==3.10.0  # Speech to text conversion
pydub==0.25.1  # Audio processing library
openai==1.3.7  # OpenAI API client

# Supabase Integration
supabase==2.1.0  # Official Supabase client
postgrest==0.10.8  # Supabase PostgREST client
gotrue==1.3.0  # Supabase Auth client

# Async HTTP clients
aiohttp==3.9.1  # Async HTTP client for ticker data refresh
httpx==0.24.1  # Modern HTTP client for Python (compatible with supabase 2.1.0)

# Authentication & Security
python-jose[cryptography]==3.3.0  # JWT token support
passlib[bcrypt]==1.7.4
pyotp==2.9.0
PyJWT==2.8.0  # JWT token support
python-multipart==0.0.6  # Form data support for FastAPI
email-validator==2.1.0  # Email validation for Pydantic

# System & Monitoring
psutil==5.9.6  # System monitoring
prometheus-client==0.19.0  # Prometheus metrics collection
structlog==23.2.0  # Structured logging

# Configuration & Environment
python-dotenv==1.0.0  # Environment variable management
pydantic-settings==2.1.0  # Configuration management

# Async Task Processing
tenacity==8.2.3  # Retry library for API calls and data fetching
backoff==2.2.1  # Retry library with decorators for API calls
celery==5.3.4  # Asynchronous task queue for background processing

# Testing
pytest==7.4.3
pytest-cov==4.1.0
pytest-asyncio==0.23.2

# Development Tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.5.0  # Git hooks for code quality

# Dev Runtime Tools (for watchmedo hot-reload used in docker-compose.dev)
watchdog[watchmedo]==3.0.0

# Scheduling
APScheduler==3.10.4  # Advanced Python Scheduler for cache warming jobs
pytz==2023.3  # Timezone support for scheduler

# Security
cryptography==41.0.3  # For secure secret generation and management