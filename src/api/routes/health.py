from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any

from src.core.monitoring import SystemMonitor
from src.core.logger import get_logger
from src.database.connection import engine
from src.core.config_manager import get_config

logger = get_logger(__name__)
router = APIRouter()

@router.get("/health", tags=["System"], status_code=status.HTTP_200_OK)
async def health_check() -> Dict[str, Any]:
    """
    Comprehensive system health check endpoint.
    
    Returns:
        Dict containing system health status and service readiness
    """
    try:
        # Get configuration
        config = get_config()
        
        # Perform system health check
        system_health = SystemMonitor.health_check()
        
        # Database connectivity check - use the existing sync engine
        database_status = "unknown"
        database_error = None
        database_info = {}

        try:
            from sqlalchemy import text
            from src.database.connection import configure_engine
            import socket
            import os

            # First check if we can resolve the database hostname
            db_host = "db.sgxjackuhalscowqrulv.supabase.co"
            try:
                # Set a short timeout for DNS resolution
                socket.setdefaulttimeout(3)
                ip_address = socket.gethostbyname(db_host)
                database_info["resolved_ip"] = ip_address
            except socket.gaierror as dns_error:
                logger.warning(f"Cannot resolve database hostname: {db_host}")
                database_status = "dns_failure"
                database_error = f"DNS resolution failed for {db_host}: {dns_error}"
                
                # Check if fallback IP is configured
                fallback_ip = os.environ.get('SUPABASE_FALLBACK_IP')
                if fallback_ip:
                    database_info["using_fallback_ip"] = fallback_ip
                    logger.info(f"Using fallback IP for database connection: {fallback_ip}")
                else:
                    # Return early with DNS failure if no fallback is available
                    database_info["resolution_status"] = "failed"
                    database_info["dns_error"] = str(dns_error)

            # Ensure engine is configured before attempting to connect
            db_engine = engine or configure_engine()
            
            if db_engine is None:
                raise ValueError("Database engine is not initialized")
                
            # Use the initialized engine from src.database.connection with a short timeout
            with db_engine.connect() as connection:
                # Execute a simple query with a timeout
                connection.execute(text("SELECT 1"))
                database_status = "healthy"
                database_info["connection_test"] = "successful"
        except Exception as db_error:
            logger.warning(f"Database health check failed: {db_error}")
            database_status = "unreachable"
            database_error = str(db_error)
            database_info["connection_test"] = "failed"
            database_info["error_type"] = db_error.__class__.__name__
        
        # Determine overall health status
        # Don't fail health check just because database is unreachable
        # Safely extract metrics with fallbacks for error cases
        cpu_percent = system_health.get('cpu_metrics', {}).get('cpu_percent', 0)
        memory_percent = system_health.get('memory_metrics', {}).get('memory_percent', 0)
        disk_percent = system_health.get('disk_metrics', {}).get('disk_percent', 0)
        
        # Check if any metrics have errors
        has_errors = any([
            'error' in system_health.get('cpu_metrics', {}),
            'error' in system_health.get('memory_metrics', {}),
            'error' in system_health.get('disk_metrics', {})
        ])
        
        if has_errors:
            system_healthy = False
        else:
            # Use reasonable defaults for thresholds
            max_cpu = 90  # 90% CPU threshold
            max_memory = 90  # 90% memory threshold
            max_disk = 90  # 90% disk threshold
            
            system_healthy = all([
                cpu_percent < max_cpu,
                memory_percent < max_memory,
                disk_percent < max_disk
            ])
        
        # Overall status is healthy if system is healthy, regardless of database
        overall_status = "healthy" if system_healthy else "degraded"
        
        # Combine health checks
        health_status = {
            "status": overall_status,
            "system": system_health,
            "database": {
                "status": database_status,
                "error": database_error
            },
            "services": {
                "api": "healthy",
                "bot": "healthy"  # Add more service checks as needed
            }
        }
        
        return health_status
    
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE, 
            detail="System health check failed"
        )

@router.get("/test", tags=["System"])
async def test_endpoint():
    """Simple test endpoint to verify API functionality"""
    return {"message": "API test successful", "timestamp": "2025-08-26"} 