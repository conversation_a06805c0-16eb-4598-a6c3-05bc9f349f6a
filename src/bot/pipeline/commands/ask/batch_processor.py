"""
Batch Query Processor for Ask Pipeline

This module implements batch query processing for the /ask command,
allowing users to ask questions about multiple stocks at once.
"""

import asyncio
import re
from typing import Dict, Any, List, Optional, Tuple
import time
import logging
from datetime import datetime

from src.core.logger import get_logger
from .pipeline import execute_ask_pipeline
from ...core.context_manager import PipelineContext, PipelineStatus

logger = get_logger(__name__)

class BatchQueryProcessor:
    """
    Processes batch queries for multiple symbols
    
    Features:
    - Automatic symbol extraction from queries
    - Parallel processing of queries for multiple symbols
    - Result aggregation and formatting
    - Concurrency control to prevent overloading
    - Language detection support
    """
    
    def __init__(self, max_concurrency: int = 3):
        """
        Initialize the batch query processor
        
        Args:
            max_concurrency: Maximum number of concurrent queries to process
        """
        self.max_concurrency = max_concurrency
        self.semaphore = asyncio.Semaphore(max_concurrency)
    
    async def process_batch_query(
        self,
        query: str,
        symbols: List[str],
        user_id: Optional[str] = None,
        guild_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        strict_mode: bool = False
    ) -> PipelineContext:
        """
        Process a batch query for multiple symbols
        
        Args:
            query: The base query to process
            symbols: List of symbols to process the query for
            user_id: Discord user ID
            guild_id: Discord guild ID
            correlation_id: Correlation ID for request tracing
            strict_mode: Whether to enforce strict templating
            
        Returns:
            PipelineContext with aggregated results
        """
        logger.info(f"Processing batch query for {len(symbols)} symbols: {', '.join(symbols)}")
        
        # Create a master context for the batch query
        master_context = PipelineContext(
            command_name="batch_ask",
            original_query=query,
            user_id=user_id,
            guild_id=guild_id,
            correlation_id=correlation_id
        )
        master_context.processing_results["symbols"] = symbols
        master_context.processing_results["batch_query"] = query
        master_context.processing_results["individual_results"] = {}
        
        # Process each symbol in parallel with concurrency control
        start_time = time.time()
        tasks = []
        
        for symbol in symbols:
            # Create a symbol-specific query
            symbol_query = self._create_symbol_query(query, symbol)
            
            # Create a task for processing this symbol
            task = asyncio.create_task(
                self._process_symbol_query(
                    symbol,
                    symbol_query,
                    user_id,
                    guild_id,
                    correlation_id,
                    strict_mode
                )
            )
            tasks.append(task)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        successful_symbols = []
        failed_symbols = []
        
        for i, result in enumerate(results):
            symbol = symbols[i]
            
            if isinstance(result, Exception):
                logger.error(f"Error processing query for {symbol}: {result}")
                failed_symbols.append(symbol)
                master_context.error_log.append({
                    "symbol": symbol,
                    "error_message": str(result),
                    "error_type": type(result).__name__,
                    "timestamp": datetime.now().isoformat()
                })
                master_context.processing_results["individual_results"][symbol] = {
                    "status": "failed",
                    "error": str(result)
                }
            elif isinstance(result, tuple) and len(result) == 2:
                symbol_context, response = result
                
                if symbol_context.status == PipelineStatus.COMPLETED:
                    successful_symbols.append(symbol)
                    master_context.processing_results["individual_results"][symbol] = {
                        "status": "completed",
                        "response": response
                    }
                else:
                    failed_symbols.append(symbol)
                    master_context.processing_results["individual_results"][symbol] = {
                        "status": "failed",
                        "error": "Pipeline execution failed",
                        "error_log": symbol_context.error_log
                    }
            else:
                failed_symbols.append(symbol)
                master_context.processing_results["individual_results"][symbol] = {
                    "status": "failed",
                    "error": "Unexpected result format"
                }
        
        # Generate aggregated response
        aggregated_response = self._generate_aggregated_response(
            query, symbols, successful_symbols, failed_symbols, 
            master_context.processing_results["individual_results"]
        )
        
        master_context.processing_results["response"] = aggregated_response
        master_context.status = PipelineStatus.COMPLETED
        master_context.completion_time = datetime.now()
        master_context.execution_time = time.time() - start_time
        
        logger.info(f"Batch query processing completed in {master_context.execution_time:.2f}s")
        logger.info(f"Successful symbols: {len(successful_symbols)}/{len(symbols)}")
        
        return master_context
    
    async def _process_symbol_query(
        self,
        symbol: str,
        query: str,
        user_id: Optional[str] = None,
        guild_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        strict_mode: bool = False
    ) -> Tuple[PipelineContext, str]:
        """
        Process a query for a specific symbol with concurrency control
        
        Args:
            symbol: The symbol to process
            query: The symbol-specific query
            user_id: Discord user ID
            guild_id: Discord guild ID
            correlation_id: Correlation ID for request tracing
            strict_mode: Whether to enforce strict templating
            
        Returns:
            Tuple of (PipelineContext, response_text)
        """
        async with self.semaphore:
            logger.info(f"Processing query for {symbol}: {query}")
            
            try:
                # Execute the ask pipeline for this symbol
                context = await execute_ask_pipeline(
                    query=query,
                    user_id=user_id,
                    guild_id=guild_id,
                    correlation_id=f"{correlation_id}_{symbol}" if correlation_id else None,
                    strict_mode=strict_mode
                )
                
                # Extract response
                response = context.processing_results.get("response", "")
                
                return context, response
                
            except Exception as e:
                logger.error(f"Error processing query for {symbol}: {e}")
                raise
    
    def _create_symbol_query(self, base_query: str, symbol: str) -> str:
        """
        Create a symbol-specific query from the base query
        
        Args:
            base_query: The base query template
            symbol: The symbol to insert
            
        Returns:
            Symbol-specific query
        """
        # Check if the query already contains the symbol
        if symbol in base_query:
            return base_query
        
        # Check if the query contains a placeholder for the symbol
        if "{symbol}" in base_query:
            return base_query.format(symbol=symbol)
        
        # Otherwise, append the symbol to the query
        return f"{base_query} for {symbol}"
    
    def _generate_aggregated_response(
        self,
        query: str,
        symbols: List[str],
        successful_symbols: List[str],
        failed_symbols: List[str],
        individual_results: Dict[str, Dict[str, Any]]
    ) -> str:
        """
        Generate an aggregated response for the batch query
        
        Args:
            query: The original query
            symbols: List of all symbols
            successful_symbols: List of successfully processed symbols
            failed_symbols: List of failed symbols
            individual_results: Individual results for each symbol
            
        Returns:
            Aggregated response text
        """
        # Create header
        header = f"# Batch Analysis: {len(symbols)} Symbols\n\n"
        header += f"**Query:** {query}\n\n"
        
        # Add summary
        summary = f"**Summary:** Processed {len(successful_symbols)}/{len(symbols)} symbols successfully"
        if failed_symbols:
            summary += f" ({len(failed_symbols)} failed: {', '.join(failed_symbols)})"
        summary += "\n\n"
        
        # Add individual results
        results_section = ""
        
        for symbol in symbols:
            result = individual_results.get(symbol, {})
            status = result.get("status", "unknown")
            
            if status == "completed":
                response = result.get("response", "")
                
                # Extract the most relevant part of the response
                # This is a simplified extraction - you might want to implement
                # a more sophisticated extraction based on your response format
                relevant_response = self._extract_relevant_response(response, symbol)
                
                results_section += f"## {symbol}\n\n{relevant_response}\n\n"
            else:
                error = result.get("error", "Unknown error")
                results_section += f"## {symbol}\n\n❌ Failed: {error}\n\n"
        
        # Add disclaimer
        disclaimer = (
            "*This batch analysis is for informational purposes only and does not constitute "
            "financial advice. Always do your own research before making investment decisions.*"
        )
        
        # Combine all sections
        return header + summary + results_section + disclaimer
    
    def _extract_relevant_response(self, response: str, symbol: str) -> str:
        """
        Extract the most relevant part of a response for a symbol
        
        Args:
            response: The full response
            symbol: The symbol to extract information for
            
        Returns:
            Relevant part of the response
        """
        # If the response is short, return it as is
        if len(response) < 500:
            return response
        
        # Try to extract sections with the symbol name
        symbol_pattern = re.compile(
            r"(.*?" + re.escape(symbol) + r".*?)(?=\n\n|\Z)",
            re.DOTALL
        )
        symbol_matches = symbol_pattern.findall(response)
        
        if symbol_matches:
            # Return the first few matches, up to 500 characters
            combined = "\n\n".join(symbol_matches[:3])
            if len(combined) > 500:
                return combined[:500] + "..."
            return combined
        
        # If no symbol-specific sections found, return the beginning of the response
        if len(response) > 500:
            return response[:500] + "..."
        return response

def extract_symbols_from_query(query: str) -> List[str]:
    """
    Extract stock symbols from a query - ONLY symbols with $ prefix

    Args:
        query: The query to extract symbols from

    Returns:
        List of extracted symbols
    """
    # FIXED: Only extract symbols with $ prefix to prevent false positives
    # Pattern for $SYMBOL only (1-5 uppercase letters after $)
    symbol_pattern = r'\$([A-Z]{1,5})\b'

    # Find all matches
    matches = re.findall(symbol_pattern, query)

    # Filter out common false positives
    symbols = []
    excluded_words = {
        "AND", "OR", "FOR", "THE", "A", "AN", "IN", "ON", "AT", "BY",
        "IS", "ARE", "WAS", "WERE", "BE", "BEEN", "HAVE", "HAS", "HAD",
        "DO", "DOES", "DID", "WILL", "WOULD", "COULD", "SHOULD", "MAY",
        "MIGHT", "CAN", "WHAT", "WHERE", "WHEN", "WHY", "HOW", "WHO",
        "WHICH", "THAT", "THIS", "THESE", "THOSE", "OF", "TO", "FROM",
        "WITH", "WITHOUT", "ABOUT", "ABOVE", "BELOW", "UNDER", "OVER",
        "PRICE", "STOCK", "TRADE", "BUY", "SELL", "HOLD"
    }

    for symbol in matches:
        if symbol and symbol not in excluded_words:
            symbols.append(symbol)

    # Remove duplicates while preserving order
    unique_symbols = []
    for symbol in symbols:
        if symbol not in unique_symbols:
            unique_symbols.append(symbol)
    
    return unique_symbols


async def execute_batch_ask_pipeline(
    query: str,
    symbols: Optional[List[str]] = None,
    user_id: Optional[str] = None,
    guild_id: Optional[str] = None,
    correlation_id: Optional[str] = None,
    strict_mode: bool = False,
    max_concurrency: int = 3
) -> PipelineContext:
    """
    Execute the batch ask pipeline
    
    Args:
        query: The query to process
        symbols: Optional list of symbols to process. If not provided, symbols will be extracted from the query.
        user_id: Discord user ID
        guild_id: Discord guild ID
        correlation_id: Correlation ID for request tracing
        strict_mode: Whether to enforce strict templating
        max_concurrency: Maximum number of concurrent queries to process
        
    Returns:
        PipelineContext with aggregated results
    """
    # Extract symbols from query if not provided
    if not symbols:
        symbols = extract_symbols_from_query(query)
    
    # If no symbols found, execute as a regular query
    if not symbols:
        logger.info(f"No symbols found in query, executing as regular query: {query}")
        return await execute_ask_pipeline(
            query=query,
            user_id=user_id,
            guild_id=guild_id,
            correlation_id=correlation_id,
            strict_mode=strict_mode
        )
    
    # Limit the number of symbols to prevent abuse
    max_symbols = 5
    if len(symbols) > max_symbols:
        logger.warning(f"Too many symbols in query ({len(symbols)}), limiting to {max_symbols}")
        symbols = symbols[:max_symbols]
    
    # Process batch query
    processor = BatchQueryProcessor(max_concurrency=max_concurrency)
    return await processor.process_batch_query(
        query=query,
        symbols=symbols,
        user_id=user_id,
        guild_id=guild_id,
        correlation_id=correlation_id,
        strict_mode=strict_mode
    )