# AI Models Configuration
# This file defines available AI models and their capabilities
# Environment variables can be used with ${VAR_NAME} syntax

# Default model configuration
defaults:
  temperature: ${AI_DEFAULT_TEMPERATURE:-0.7}
  max_tokens: ${AI_DEFAULT_MAX_TOKENS:-2000}
  timeout_ms: ${AI_DEFAULT_TIMEOUT_MS:-30000}
  cost_per_1k_tokens: ${AI_DEFAULT_COST:-0.001}
  accuracy_score: ${AI_DEFAULT_ACCURACY:-0.8}
  response_time_ms: ${AI_DEFAULT_RESPONSE_TIME:-2000}

# Model capabilities mapping
capabilities:
  basic_analysis: "BASIC_ANALYSIS"
  technical_analysis: "TECHNICAL_ANALYSIS"
  fundamental_analysis: "FUNDAMENTAL_ANALYSIS"
  sentiment_analysis: "SENTIMENT_ANALYSIS"
  complex_analysis: "COMPLEX_ANALYSIS"
  real_time_trading: "REAL_TIME_TRADING"
  educational: "EDUCATIONAL"
  risk_assessment: "RISK_ASSESSMENT"

# Available AI models
models:
  gpt-4o-mini:
    name: "GPT-4o Mini"
    provider: "OpenAI"
    model_id: "${GPT_4O_MINI_MODEL_ID:-gpt-4o-mini}"
    capabilities:
      - basic_analysis
      - technical_analysis
      - educational
    max_tokens: ${GPT_4O_MINI_MAX_TOKENS:-16384}
    cost_per_1k_tokens: ${GPT_4O_MINI_COST:-0.00015}
    response_time_ms: ${GPT_4O_MINI_RESPONSE_TIME:-2000}
    accuracy_score: ${GPT_4O_MINI_ACCURACY:-0.85}
    enabled: ${GPT_4O_MINI_ENABLED:-true}

  gpt-4o:
    name: "GPT-4o"
    provider: "OpenAI"
    model_id: "${GPT_4O_MODEL_ID:-gpt-4o}"
    capabilities:
      - basic_analysis
      - technical_analysis
      - fundamental_analysis
      - complex_analysis
      - educational
      - risk_assessment
    max_tokens: ${GPT_4O_MAX_TOKENS:-128000}
    cost_per_1k_tokens: ${GPT_4O_COST:-0.005}
    response_time_ms: ${GPT_4O_RESPONSE_TIME:-3000}
    accuracy_score: ${GPT_4O_ACCURACY:-0.92}
    enabled: ${GPT_4O_ENABLED:-true}

  claude-3-5-sonnet:
    name: "Claude 3.5 Sonnet"
    provider: "Anthropic"
    model_id: "${CLAUDE_3_5_SONNET_MODEL_ID:-claude-3-5-sonnet-20241022}"
    capabilities:
      - basic_analysis
      - technical_analysis
      - fundamental_analysis
      - complex_analysis
      - educational
      - sentiment_analysis
      - risk_assessment
    max_tokens: ${CLAUDE_3_5_SONNET_MAX_TOKENS:-200000}
    cost_per_1k_tokens: ${CLAUDE_3_5_SONNET_COST:-0.003}
    response_time_ms: ${CLAUDE_3_5_SONNET_RESPONSE_TIME:-2500}
    accuracy_score: ${CLAUDE_3_5_SONNET_ACCURACY:-0.90}
    enabled: ${CLAUDE_3_5_SONNET_ENABLED:-true}

  mixtral-8x7b:
    name: "Mixtral 8x7B"
    provider: "Mistral"
    model_id: "${MIXTRAL_8X7B_MODEL_ID:-mixtral-8x7b-instruct}"
    capabilities:
      - basic_analysis
      - technical_analysis
      - educational
    max_tokens: ${MIXTRAL_8X7B_MAX_TOKENS:-32768}
    cost_per_1k_tokens: ${MIXTRAL_8X7B_COST:-0.00014}
    response_time_ms: ${MIXTRAL_8X7B_RESPONSE_TIME:-1500}
    accuracy_score: ${MIXTRAL_8X7B_ACCURACY:-0.78}
    enabled: ${MIXTRAL_8X7B_ENABLED:-true}

  llama-3-8b:
    name: "Llama 3 8B"
    provider: "Meta"
    model_id: "${LLAMA_3_8B_MODEL_ID:-llama-3-8b-instruct}"
    capabilities:
      - basic_analysis
      - educational
    max_tokens: ${LLAMA_3_8B_MAX_TOKENS:-8192}
    cost_per_1k_tokens: ${LLAMA_3_8B_COST:-0.00010}
    response_time_ms: ${LLAMA_3_8B_RESPONSE_TIME:-1000}
    accuracy_score: ${LLAMA_3_8B_ACCURACY:-0.75}
    enabled: ${LLAMA_3_8B_ENABLED:-true}

# Model selection scoring weights
scoring_weights:
  accuracy: ${AI_SCORING_ACCURACY_WEIGHT:-0.4}
  cost_efficiency: ${AI_SCORING_COST_WEIGHT:-0.25}
  response_time: ${AI_SCORING_TIME_WEIGHT:-0.2}
  token_capacity: ${AI_SCORING_CAPACITY_WEIGHT:-0.1}
  user_preference: ${AI_SCORING_PREFERENCE_WEIGHT:-0.05}

# Complexity multipliers for token estimation
complexity_multipliers:
  simple: ${AI_COMPLEXITY_SIMPLE:-1.0}
  moderate: ${AI_COMPLEXITY_MODERATE:-1.5}
  complex: ${AI_COMPLEXITY_COMPLEX:-2.0}
  expert: ${AI_COMPLEXITY_EXPERT:-2.5}
  real_time: ${AI_COMPLEXITY_REAL_TIME:-3.0}

# Performance tracking settings
performance:
  enable_tracking: ${AI_PERFORMANCE_TRACKING:-true}
  metrics_retention_days: ${AI_METRICS_RETENTION:-30}
  circuit_breaker_threshold: ${AI_CIRCUIT_BREAKER_THRESHOLD:-5}
  circuit_breaker_timeout: ${AI_CIRCUIT_BREAKER_TIMEOUT:-300}
