"""
Enhanced Depth and Style Analyzer

Advanced analyzer that uses weighted scoring, contextual awareness, and NLP techniques
to determine appropriate response depth and style for trading queries.
"""

import logging
import re
from typing import Dict, Any, List, Tuple, Optional, Set
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict, Counter

from .response_templates import ResponseDepth, ResponseStyle

logger = logging.getLogger(__name__)

@dataclass
class AnalysisResult:
    """Result of query analysis with confidence scores"""
    depth: ResponseDepth
    style: ResponseStyle
    depth_confidence: float
    style_confidence: float
    reasoning: List[str] = field(default_factory=list)
    alternative_suggestions: List[Tuple[ResponseDepth, ResponseStyle, float]] = field(default_factory=list)

@dataclass
class QueryFeatures:
    """Extracted features from query analysis"""
    word_count: int
    sentence_count: int
    question_count: int
    technical_terms: Set[str] = field(default_factory=set)
    greeting_present: bool = False
    urgency_indicators: Set[str] = field(default_factory=set)
    complexity_score: float = 0.0
    politeness_level: float = 0.0
    specificity_score: float = 0.0

class EnhancedDepthAnalyzer:
    """Advanced depth analyzer with weighted scoring and contextual awareness"""
    
    def __init__(self):
        # Weighted indicators with confidence scores
        self.depth_indicators = {
            ResponseDepth.CASUAL: {
                'greetings': (['quick', 'brief', 'simple', 'short', 'basic'], 0.8),
                'social': (['hello', 'hi', 'hey', 'thanks', 'thank you', 'bye', 'goodbye'], 0.9),
                'casual_phrases': (['how are you', 'what\'s up', 'just curious'], 0.7),
                'brevity_requests': (['tldr', 'summary', 'in short', 'quickly'], 0.85)
            },
            ResponseDepth.GENERAL: {
                'basic_questions': (['explain', 'tell me', 'what is', 'how does', 'show me'], 0.7),
                'standard_requests': (['give me', 'price', 'stock', 'trading', 'market'], 0.6),
                'comparison': (['compare', 'vs', 'versus', 'difference between'], 0.65),
                'general_inquiry': (['information about', 'details on', 'overview of'], 0.6)
            },
            ResponseDepth.DETAILED: {
                'depth_requests': (['detailed', 'comprehensive', 'thorough', 'deep', 'in-depth'], 0.9),
                'analysis_terms': (['analysis', 'breakdown', 'examination', 'evaluation'], 0.8),
                'financial_detail': (['earnings', 'revenue', 'financials', 'balance sheet'], 0.85),
                'research_oriented': (['research', 'investigate', 'study', 'explore fully'], 0.8)
            },
            ResponseDepth.TECHNICAL: {
                'technical_analysis': (['technical analysis', 'chart analysis', 'price action'], 0.95),
                'indicators': (['rsi', 'macd', 'moving average', 'bollinger bands', 'stochastic'], 0.9),
                'patterns': (['support', 'resistance', 'trend', 'pattern', 'breakout'], 0.85),
                'advanced_tools': (['fibonacci', 'elliott wave', 'candlestick', 'volume profile'], 0.9)
            },
            ResponseDepth.ACADEMIC: {
                'learning': (['learn', 'understand', 'education', 'tutorial', 'guide'], 0.8),
                'foundational': (['basics', 'fundamentals', 'principles', 'concepts'], 0.85),
                'theory': (['theory', 'methodology', 'framework', 'model'], 0.9),
                'step_by_step': (['step by step', 'walkthrough', 'from scratch'], 0.8)
            }
        }
        
        # Context modifiers
        self.context_modifiers = {
            'user_expertise': {
                'beginner': {ResponseDepth.ACADEMIC: 0.3, ResponseDepth.CASUAL: 0.2},
                'intermediate': {ResponseDepth.GENERAL: 0.2, ResponseDepth.DETAILED: 0.1},
                'advanced': {ResponseDepth.TECHNICAL: 0.3, ResponseDepth.DETAILED: 0.2}
            },
            'time_constraint': {
                'urgent': {ResponseDepth.CASUAL: 0.4, ResponseDepth.GENERAL: 0.2},
                'relaxed': {ResponseDepth.DETAILED: 0.2, ResponseDepth.ACADEMIC: 0.1}
            }
        }
        
        # Technical term recognition
        self.technical_terms = {
            'basic': ['stock', 'price', 'market', 'trading', 'buy', 'sell'],
            'intermediate': ['dividend', 'earnings', 'pe ratio', 'market cap', 'volume'],
            'advanced': ['rsi', 'macd', 'fibonacci', 'support', 'resistance', 'volatility'],
            'expert': ['vwap', 'elliott wave', 'ichimoku', 'stochastic', 'williams r']
        }
    
    def analyze_depth(self, query: str, context_clues: Dict[str, Any], features: QueryFeatures) -> Tuple[ResponseDepth, float, List[str]]:
        """Analyze query depth with weighted scoring"""
        
        scores = defaultdict(float)
        reasoning = []
        query_lower = query.lower()
        
        # Apply indicator-based scoring
        for depth, categories in self.depth_indicators.items():
            for category, (indicators, weight) in categories.items():
                matches = [ind for ind in indicators if ind in query_lower]
                if matches:
                    score_boost = len(matches) * weight * 0.2
                    scores[depth] += score_boost
                    reasoning.append(f"Found {category} indicators: {matches} (+{score_boost:.2f} for {depth.value})")
        
        # Apply contextual modifiers
        if context_clues:
            for context_key, context_value in context_clues.items():
                if context_key in self.context_modifiers and context_value in self.context_modifiers[context_key]:
                    modifiers = self.context_modifiers[context_key][context_value]
                    for depth, modifier in modifiers.items():
                        scores[depth] += modifier
                        reasoning.append(f"Context {context_key}={context_value} boosted {depth.value} by {modifier}")
        
        # Apply feature-based adjustments
        scores = self._apply_feature_adjustments(scores, features, reasoning)
        
        # Apply technical complexity scoring
        tech_level = self._assess_technical_complexity(query_lower)
        if tech_level >= 0.7:
            scores[ResponseDepth.TECHNICAL] += 0.3
            reasoning.append(f"High technical complexity ({tech_level:.2f}) boosted TECHNICAL depth")
        elif tech_level >= 0.5:
            scores[ResponseDepth.DETAILED] += 0.2
            reasoning.append(f"Medium technical complexity ({tech_level:.2f}) boosted DETAILED depth")
        
        # Determine best depth
        if not scores:
            return self._default_depth_from_features(features), 0.5, ["No strong indicators found, using feature-based default"]
        
        best_depth = max(scores, key=scores.get)
        confidence = min(1.0, scores[best_depth])
        
        return best_depth, confidence, reasoning
    
    def _apply_feature_adjustments(self, scores: Dict, features: QueryFeatures, reasoning: List[str]) -> Dict:
        """Apply feature-based score adjustments"""
        
        # Word count influence
        if features.word_count <= 3:
            scores[ResponseDepth.CASUAL] += 0.3
            reasoning.append(f"Short query ({features.word_count} words) boosted CASUAL depth")
        elif features.word_count >= 15:
            scores[ResponseDepth.DETAILED] += 0.2
            reasoning.append(f"Long query ({features.word_count} words) boosted DETAILED depth")
        
        # Question pattern influence
        if features.question_count > 1:
            scores[ResponseDepth.DETAILED] += 0.2
            reasoning.append(f"Multiple questions ({features.question_count}) suggest need for detailed response")
        
        # Complexity influence
        if features.complexity_score > 0.7:
            scores[ResponseDepth.TECHNICAL] += 0.25
            scores[ResponseDepth.DETAILED] += 0.15
            reasoning.append(f"High complexity score ({features.complexity_score:.2f}) boosted technical depths")
        
        # Specificity influence
        if features.specificity_score > 0.8:
            scores[ResponseDepth.DETAILED] += 0.2
            reasoning.append(f"High specificity ({features.specificity_score:.2f}) suggests detailed response needed")
        
        return scores
    
    def _assess_technical_complexity(self, query: str) -> float:
        """Assess technical complexity of query"""
        complexity_score = 0.0
        total_terms = 0
        
        for level, terms in self.technical_terms.items():
            level_weight = {'basic': 0.1, 'intermediate': 0.3, 'advanced': 0.6, 'expert': 1.0}[level]
            found_terms = sum(1 for term in terms if term in query)
            total_terms += len(terms)
            complexity_score += found_terms * level_weight
        
        return min(1.0, complexity_score / 10)  # Normalize
    
    def _default_depth_from_features(self, features: QueryFeatures) -> ResponseDepth:
        """Determine default depth from query features when no indicators match"""
        if features.greeting_present:
            return ResponseDepth.CASUAL
        elif features.word_count <= 5:
            return ResponseDepth.CASUAL
        elif features.complexity_score > 0.6:
            return ResponseDepth.DETAILED
        else:
            return ResponseDepth.GENERAL

class EnhancedStyleAnalyzer:
    """Advanced style analyzer with contextual awareness"""
    
    def __init__(self):
        # Weighted style indicators - FIXED: Use correct ResponseStyle enum values
        self.style_indicators = {
            ResponseStyle.SIMPLE: {
                'greetings': (['hello', 'hi', 'hey', 'good morning', 'good afternoon'], 0.9),
                'social': (['how are you', 'thanks', 'thank you', 'please', 'appreciate'], 0.8),
                'casual_expressions': (['yeah', 'cool', 'awesome', 'great', 'nice'], 0.7),
                'personal': (['i think', 'i feel', 'in my opinion', 'personally'], 0.6)
            },
            ResponseStyle.DETAILED: {
                'business_terms': (['report', 'analysis', 'data', 'information', 'status'], 0.8),
                'formal_requests': (['please provide', 'could you', 'i would like', 'request'], 0.7),
                'market_language': (['market conditions', 'trading volume', 'price action'], 0.8),
                'objective_tone': (['facts', 'data', 'statistics', 'metrics'], 0.7)
            },
            ResponseStyle.ACADEMIC: {
                'learning_intent': (['what is', 'how does', 'explain', 'teach me'], 0.9),
                'knowledge_seeking': (['learn', 'understand', 'clarify', 'help me grasp'], 0.8),
                'beginner_language': (['new to', 'beginner', 'start', 'basics'], 0.85),
                'step_requests': (['step by step', 'guide', 'tutorial', 'walkthrough'], 0.8)
            },
            ResponseStyle.TECHNICAL: {
                'analysis_requests': (['analyze', 'examine', 'evaluate', 'assess'], 0.9),
                'technical_depth': (['technical analysis', 'fundamental analysis', 'deep dive'], 0.95),
                'comparison': (['compare', 'contrast', 'pros and cons', 'advantages'], 0.8),
                'critical_thinking': (['why', 'how', 'what factors', 'implications'], 0.7)
            },
            ResponseStyle.TRADING: {
                'humor_indicators': (['funny', 'joke', 'humor', 'lol', 'haha'], 0.9),
                'playful': (['entertaining', 'fun', 'amusing', 'light'], 0.7),
                'trading_terms': (['buy', 'sell', 'hold', 'position', 'entry', 'exit'], 0.8),
                'casual_trading': (['meme', 'stonks', 'to the moon', 'diamond hands'], 0.8)
            }
        }
        
        # Tone analysis patterns
        self.tone_patterns = {
            'formal': re.compile(r'\b(please|kindly|would|could|may|might)\b', re.IGNORECASE),
            'urgent': re.compile(r'\b(urgent|asap|quickly|immediately|now|fast)\b', re.IGNORECASE),
            'uncertain': re.compile(r'\b(maybe|perhaps|possibly|might|could be)\b', re.IGNORECASE),
            'confident': re.compile(r'\b(definitely|certainly|sure|confident|know)\b', re.IGNORECASE)
        }
    
    def analyze_style(self, query: str, context_clues: Dict[str, Any], features: QueryFeatures) -> Tuple[ResponseStyle, float, List[str]]:
        """Analyze query style with advanced techniques"""
        
        scores = defaultdict(float)
        reasoning = []
        query_lower = query.lower()
        
        # Apply indicator-based scoring
        for style, categories in self.style_indicators.items():
            for category, (indicators, weight) in categories.items():
                matches = [ind for ind in indicators if ind in query_lower]
                if matches:
                    score_boost = len(matches) * weight * 0.25
                    scores[style] += score_boost
                    reasoning.append(f"Found {category}: {matches} (+{score_boost:.2f} for {style.value})")
        
        # Apply tone analysis
        scores = self._apply_tone_analysis(scores, query, reasoning)
        
        # Apply contextual adjustments
        scores = self._apply_contextual_style_adjustments(scores, context_clues, features, reasoning)
        
        # Apply feature-based adjustments
        scores = self._apply_style_feature_adjustments(scores, features, reasoning)
        
        # Determine best style
        if not scores:
            return self._default_style_from_features(features), 0.5, ["No strong indicators found, using default"]
        
        best_style = max(scores, key=scores.get)
        confidence = min(1.0, scores[best_style])
        
        return best_style, confidence, reasoning
    
    def _apply_tone_analysis(self, scores: Dict, query: str, reasoning: List[str]) -> Dict:
        """Apply tone-based style adjustments"""
        
        for tone, pattern in self.tone_patterns.items():
            if pattern.search(query):
                if tone == 'formal':
                    scores[ResponseStyle.DETAILED] += 0.3
                    reasoning.append(f"Formal tone detected, boosted DETAILED style")
                elif tone == 'urgent':
                    scores[ResponseStyle.DETAILED] += 0.2
                    reasoning.append(f"Urgent tone detected, boosted DETAILED style")
                elif tone == 'uncertain':
                    scores[ResponseStyle.ACADEMIC] += 0.2
                    reasoning.append(f"Uncertain tone suggests academic need")
                elif tone == 'confident':
                    scores[ResponseStyle.TECHNICAL] += 0.2
                    reasoning.append(f"Confident tone suggests technical preference")
        
        return scores
    
    def _apply_contextual_style_adjustments(self, scores: Dict, context_clues: Dict[str, Any], 
                                          features: QueryFeatures, reasoning: List[str]) -> Dict:
        """Apply contextual style adjustments"""
        
        if not context_clues:
            return scores
        
        # User expertise influence
        expertise = context_clues.get('user_expertise')
        if expertise == 'beginner':
            scores[ResponseStyle.ACADEMIC] += 0.3
            reasoning.append("Beginner expertise level boosted ACADEMIC style")
        elif expertise == 'advanced':
            scores[ResponseStyle.TECHNICAL] += 0.3
            reasoning.append("Advanced expertise level boosted TECHNICAL style")
        
        # Interaction history
        previous_style = context_clues.get('previous_preferred_style')
        if previous_style:
            try:
                prev_style_enum = ResponseStyle(previous_style)
                scores[prev_style_enum] += 0.15
                reasoning.append(f"Previous preference for {previous_style} style applied")
            except ValueError:
                pass
        
        # Time context
        time_context = context_clues.get('time_context')
        if time_context == 'business_hours':
            scores[ResponseStyle.DETAILED] += 0.1
        elif time_context == 'casual_hours':
            scores[ResponseStyle.SIMPLE] += 0.1
        
        return scores
    
    def _apply_style_feature_adjustments(self, scores: Dict, features: QueryFeatures, 
                                       reasoning: List[str]) -> Dict:
        """Apply feature-based style adjustments"""
        
        # Politeness level influence
        if features.politeness_level > 0.7:
            scores[ResponseStyle.DETAILED] += 0.2
            reasoning.append(f"High politeness level ({features.politeness_level:.2f}) boosted DETAILED")

        # Greeting influence
        if features.greeting_present:
            scores[ResponseStyle.SIMPLE] += 0.3
            reasoning.append("Greeting detected, boosted SIMPLE style")

        # Technical terms influence
        if len(features.technical_terms) > 3:
            scores[ResponseStyle.TECHNICAL] += 0.2
            reasoning.append(f"Multiple technical terms ({len(features.technical_terms)}) suggest TECHNICAL style")
        
        return scores
    
    def _default_style_from_features(self, features: QueryFeatures) -> ResponseStyle:
        """Determine default style from features"""
        if features.greeting_present:
            return ResponseStyle.SIMPLE
        elif len(features.technical_terms) > 0:
            return ResponseStyle.DETAILED
        elif features.question_count > 0:
            return ResponseStyle.ACADEMIC
        else:
            return ResponseStyle.DETAILED

class QueryFeatureExtractor:
    """Extracts features from queries for analysis"""
    
    def __init__(self):
        self.greeting_patterns = [
            r'\b(hello|hi|hey|good morning|good afternoon|good evening)\b',
            r'\b(thanks|thank you|thx)\b'
        ]
        
        self.urgency_patterns = [
            r'\b(urgent|asap|quickly|immediately|now|fast|hurry)\b',
            r'\b(need.*now|right away|time sensitive)\b'
        ]
        
        self.technical_financial_terms = {
            'rsi', 'macd', 'moving average', 'bollinger bands', 'fibonacci',
            'support', 'resistance', 'breakout', 'trend', 'pattern',
            'volume', 'volatility', 'market cap', 'pe ratio', 'earnings',
            'dividend', 'yield', 'beta', 'alpha', 'sharpe ratio'
        }
    
    def extract_features(self, query: str) -> QueryFeatures:
        """Extract comprehensive features from query"""
        
        words = query.split()
        sentences = query.split('.')
        questions = query.count('?')
        
        # Detect greetings
        greeting_present = any(
            re.search(pattern, query, re.IGNORECASE) 
            for pattern in self.greeting_patterns
        )
        
        # Detect urgency indicators
        urgency_indicators = set()
        for pattern in self.urgency_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            urgency_indicators.update(matches)
        
        # Detect technical terms
        query_lower = query.lower()
        technical_terms = {term for term in self.technical_financial_terms if term in query_lower}
        
        # Calculate complexity score
        complexity_score = self._calculate_complexity(query, technical_terms)
        
        # Calculate politeness score
        politeness_level = self._calculate_politeness(query)
        
        # Calculate specificity score
        specificity_score = self._calculate_specificity(query, words)
        
        return QueryFeatures(
            word_count=len(words),
            sentence_count=len([s for s in sentences if s.strip()]),
            question_count=questions,
            technical_terms=technical_terms,
            greeting_present=greeting_present,
            urgency_indicators=urgency_indicators,
            complexity_score=complexity_score,
            politeness_level=politeness_level,
            specificity_score=specificity_score
        )
    
    def _calculate_complexity(self, query: str, technical_terms: Set[str]) -> float:
        """Calculate query complexity score"""
        factors = []
        
        # Technical term density
        word_count = len(query.split())
        tech_density = len(technical_terms) / max(1, word_count)
        factors.append(tech_density * 2)
        
        # Sentence structure complexity
        subordinate_clauses = query.count(',') + query.count(';') + query.count(' and ') + query.count(' but ')
        structure_complexity = min(1.0, subordinate_clauses / 5)
        factors.append(structure_complexity)
        
        # Question complexity
        question_words = ['why', 'how', 'what', 'when', 'where', 'which']
        complex_questions = sum(1 for word in question_words if word in query.lower())
        question_complexity = min(1.0, complex_questions / 3)
        factors.append(question_complexity)
        
        return sum(factors) / len(factors)
    
    def _calculate_politeness(self, query: str) -> float:
        """Calculate politeness level"""
        polite_indicators = [
            'please', 'thank you', 'thanks', 'could you', 'would you',
            'may i', 'if possible', 'kindly', 'appreciate'
        ]
        
        query_lower = query.lower()
        polite_count = sum(1 for indicator in polite_indicators if indicator in query_lower)
        
        return min(1.0, polite_count / 3)
    
    def _calculate_specificity(self, query: str, words: List[str]) -> float:
        """Calculate how specific the query is"""
        factors = []
        
        # Proper noun density (approximated by capitalized words)
        proper_nouns = sum(1 for word in words if word[0].isupper() and len(word) > 1)
        proper_noun_density = proper_nouns / max(1, len(words))
        factors.append(proper_noun_density * 2)
        
        # Number presence (specific values)
        numbers = len(re.findall(r'\b\d+\.?\d*\b', query))
        number_density = numbers / max(1, len(words))
        factors.append(number_density * 3)
        
        # Specific request words
        specific_words = ['specific', 'exactly', 'precisely', 'particular', 'detailed']
        specific_count = sum(1 for word in specific_words if word in query.lower())
        factors.append(min(1.0, specific_count / 2))
        
        return sum(factors) / len(factors)

class EnhancedQueryAnalyzer:
    """Enhanced query analyzer combining all improvements"""
    
    def __init__(self):
        self.depth_analyzer = EnhancedDepthAnalyzer()
        self.style_analyzer = EnhancedStyleAnalyzer()
        self.feature_extractor = QueryFeatureExtractor()
    
    def analyze_query_comprehensive(self, query: str, context_clues: Dict[str, Any]) -> AnalysisResult:
        """Comprehensive query analysis with confidence scoring and alternatives"""
        
        # Extract features
        features = self.feature_extractor.extract_features(query)
        
        # Analyze depth and style
        depth, depth_confidence, depth_reasoning = self.depth_analyzer.analyze_depth(
            query, context_clues, features
        )
        
        style, style_confidence, style_reasoning = self.style_analyzer.analyze_style(
            query, context_clues, features
        )
        
        # Combine reasoning
        all_reasoning = depth_reasoning + style_reasoning
        
        # Generate alternatives
        alternatives = self._generate_alternatives(query, context_clues, features, depth, style)
        
        logger.info(f"Enhanced analysis - Depth: {depth.value} ({depth_confidence:.2f}), "
                   f"Style: {style.value} ({style_confidence:.2f})")
        
        return AnalysisResult(
            depth=depth,
            style=style,
            depth_confidence=depth_confidence,
            style_confidence=style_confidence,
            reasoning=all_reasoning,
            alternative_suggestions=alternatives
        )
    
    def _generate_alternatives(self, query: str, context_clues: Dict[str, Any], 
                             features: QueryFeatures, primary_depth: ResponseDepth, 
                             primary_style: ResponseStyle) -> List[Tuple[ResponseDepth, ResponseStyle, float]]:
        """Generate alternative depth/style combinations with confidence scores"""
        
        alternatives = []
        
        # If confidence is low, suggest alternatives
        depth_scores = defaultdict(float)
        style_scores = defaultdict(float)
        
        # Re-run analysis to get all scores (simplified version)
        for depth in ResponseDepth:
            if depth != primary_depth:
                alt_depth, alt_confidence, _ = self.depth_analyzer.analyze_depth(query, context_clues, features)
                if alt_confidence > 0.3:
                    depth_scores[depth] = alt_confidence * 0.8  # Lower than primary
        
        for style in ResponseStyle:
            if style != primary_style:
                alt_style, alt_confidence, _ = self.style_analyzer.analyze_style(query, context_clues, features)
                if alt_confidence > 0.3:
                    style_scores[style] = alt_confidence * 0.8  # Lower than primary
        
        # Generate combinations
        for alt_depth, depth_score in depth_scores.items():
            for alt_style, style_score in style_scores.items():
                combined_score = (depth_score + style_score) / 2
                if combined_score > 0.4:
                    alternatives.append((alt_depth, alt_style, combined_score))
        
        # Sort by confidence and limit to top 3
        alternatives.sort(key=lambda x: x[2], reverse=True)
        return alternatives[:3]
    
    # Legacy method for backward compatibility
    def analyze_query_characteristics(self, query: str, context_clues: Dict[str, Any]) -> Tuple[ResponseDepth, ResponseStyle]:
        """Legacy method for backward compatibility"""
        result = self.analyze_query_comprehensive(query, context_clues)
        return result.depth, result.style