"""
Pipeline Sections Manager

N8N-style modular pipeline with retryable sections and quality checks.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import time

logger = logging.getLogger(__name__)

class SectionStatus(Enum):
    """Status of pipeline sections"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"
    SKIPPED = "skipped"

class QualityCheck(Enum):
    """Quality check types"""
    VALIDATION = "validation"
    COMPLETENESS = "completeness"
    ACCURACY = "accuracy"
    RELEVANCE = "relevance"
    FORMAT = "format"

@dataclass
class SectionResult:
    """Result of a pipeline section"""
    section_id: str
    status: SectionStatus
    output_data: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    execution_time: float = 0.0
    quality_score: float = 0.0
    retry_count: int = 0
    max_retries: int = 3
    quality_checks: List[Dict[str, Any]] = field(default_factory=list)

@dataclass
class PipelineSection:
    """A pipeline section that can be retried independently"""
    section_id: str
    name: str
    description: str
    dependencies: List[str] = field(default_factory=list)
    processor: Callable = None
    quality_checker: Callable = None
    max_retries: int = 3
    timeout: float = 30.0
    critical: bool = True
    fallback_processor: Optional[Callable] = None

class PipelineSectionManager:
    """Manages pipeline sections with retry logic and quality checks"""
    
    def __init__(self):
        self.sections: Dict[str, PipelineSection] = {}
        self.results: Dict[str, SectionResult] = {}
        self.execution_order: List[str] = []
        self.retry_delays = [1, 2, 5]  # Seconds between retries
        
    def add_section(self, section: PipelineSection):
        """Add a pipeline section"""
        self.sections[section.section_id] = section
        logger.info(f"Added pipeline section: {section.name} ({section.section_id})")
    
    def set_execution_order(self, order: List[str]):
        """Set the execution order of sections"""
        self.execution_order = order
        logger.info(f"Set execution order: {' -> '.join(order)}")
    
    async def execute_pipeline(self, context: Any) -> Dict[str, SectionResult]:
        """Execute the pipeline with retry logic"""
        
        logger.info("Starting pipeline execution")
        start_time = time.time()
        
        # Initialize all sections as pending
        for section_id in self.execution_order:
            self.results[section_id] = SectionResult(
                section_id=section_id,
                status=SectionStatus.PENDING
            )
        
        # Execute sections in order
        for section_id in self.execution_order:
            logger.info(f"🚀 Attempting to execute section: {section_id}")
            
            if not await self._can_execute_section(section_id):
                logger.warning(f"⏭️  Skipping section {section_id} - dependencies not met")
                self.results[section_id].status = SectionStatus.SKIPPED
                continue
            
            logger.info(f"✅ Executing section: {section_id}")
            await self._execute_section_with_retries(section_id, context)
        
        total_time = time.time() - start_time
        logger.info(f"Pipeline execution completed in {total_time:.2f}s")
        
        return self.results
    
    async def _can_execute_section(self, section_id: str) -> bool:
        """Check if a section can be executed based on dependencies"""
        
        section = self.sections[section_id]
        
        logger.info(f"🔍 Checking dependencies for section {section_id}")
        logger.info(f"   Dependencies: {section.dependencies}")
        logger.info(f"   Available results: {list(self.results.keys())}")
        
        for dep_id in section.dependencies:
            if dep_id not in self.results:
                logger.error(f"❌ Dependency {dep_id} not found for section {section_id}")
                return False
            
            dep_result = self.results[dep_id]
            logger.info(f"   Dependency {dep_id} status: {dep_result.status.value}")
            if dep_result.status != SectionStatus.COMPLETED:
                logger.warning(f"⚠️  Dependency {dep_id} not completed for section {section_id}")
                return False
        
        logger.info(f"✅ All dependencies met for section {section_id}")
        return True
    
    async def _execute_section_with_retries(self, section_id: str, context: Any):
        section = self.sections[section_id]
        result = self.results[section_id]
        
        logger.info(f"🎯 Starting execution of section: {section_id}")
        logger.info(f"   Available results at start: {list(self.results.keys())}")
        logger.info(f"   Results status: {[(k, v.status.value) for k, v in self.results.items()]}")
        
        for attempt in range(section.max_retries + 1):
            try:
                # Execute main processor
                start_time = time.time()
                logger.info(f"   🔄 Calling processor for section: {section_id}")
                output_data = await section.processor(context, self.results)
                execution_time = time.time() - start_time
                logger.info(f"   ✅ Processor completed for section: {section_id}")
                
                # Run quality checks
                quality_score, quality_checks = await self._run_quality_checks(section, output_data, context)
                
                # More lenient quality threshold
                if quality_score >= 0.2:  # Reduced from 0.4 to 0.2
                    result.status = SectionStatus.COMPLETED
                    result.output_data = output_data
                    result.quality_score = quality_score
                    result.quality_checks = quality_checks
                    result.execution_time = execution_time
                    
                    logger.info(f"Section {section.name} completed successfully (quality: {quality_score:.2%})")
                    break
                
                # If quality is low, try fallback processor
                if section.fallback_processor:
                    try:
                        fallback_data = await section.fallback_processor(context, self.results)
                        fallback_quality, fallback_checks = await self._run_quality_checks(section, fallback_data, context)
                        
                        if fallback_quality >= 0.1:  # Very low threshold for fallback
                            result.status = SectionStatus.COMPLETED
                            result.output_data = fallback_data
                            result.quality_score = fallback_quality
                            result.quality_checks = fallback_checks
                            result.execution_time = execution_time
                            
                            logger.warning(f"Section {section.name} used fallback data (quality: {fallback_quality:.2%})")
                            break
                        else:
                            result.error_message = f"Main processor and fallback failed: Low quality"
                    except Exception as e:
                        result.error_message = f"Main processor and fallback failed: {str(e)}"
                else:
                    # If no fallback processor, accept data anyway if it's close to threshold
                    if quality_score >= 0.1:  # Very lenient threshold for critical sections
                        logger.warning(f"Section {section.name} quality below threshold but accepting data (score: {quality_score:.2%})")
                        result.status = SectionStatus.COMPLETED
                        result.output_data = output_data
                        result.quality_score = quality_score
                        break
                    else:
                        result.status = SectionStatus.FAILED
                        result.error_message = f"Quality check failed after {section.max_retries} retries"
                
            except asyncio.TimeoutError:
                result.status = SectionStatus.FAILED
                result.error_message = f"Section timed out after {section.timeout}s"
                logger.error(f"Section {section.name} timed out")
                break
                
            except Exception as e:
                if attempt < section.max_retries:
                    logger.warning(f"Section {section.name} failed (attempt {attempt + 1}): {e}")
                    result.status = SectionStatus.RETRYING
                    await asyncio.sleep(self.retry_delays[min(attempt, len(self.retry_delays) - 1)])
                    continue
                else:
                    result.status = SectionStatus.FAILED
                    result.error_message = str(e)
                    logger.error(f"Section {section.name} failed after {section.max_retries} retries: {e}")
                    break
    
    async def _run_quality_checks(self, section: PipelineSection, output_data: Dict[str, Any], context: Any) -> tuple[float, List[Dict[str, Any]]]:
        """Run quality checks on section output"""
        
        if not section.quality_checker:
            return 1.0, []  # No quality checker = perfect score
        
        try:
            quality_result = await section.quality_checker(output_data, context)
            
            # Handle different quality result formats
            if isinstance(quality_result, dict):
                # Check for 'quality_score' first (from ask_sections.py)
                if 'quality_score' in quality_result:
                    score = quality_result['quality_score']
                    checks = quality_result.get('issues', [])
                # Fallback to 'score' (from pipeline_sections.py)
                elif 'score' in quality_result:
                    score = quality_result['score']
                    checks = quality_result.get('checks', [])
                else:
                    score = 0.5
                    checks = [{"type": "warning", "message": "Unknown quality result format"}]
            else:
                # If quality_result is not a dict, assume it's a score
                score = float(quality_result) if quality_result is not None else 0.5
                checks = []
            
            return score, checks
        except Exception as e:
            logger.error(f"Quality checker failed for section {section.section_id}: {e}")
            return 0.5, [{"type": "error", "message": str(e)}]
    
    def get_section_status(self, section_id: str) -> Optional[SectionStatus]:
        """Get the status of a specific section"""
        if section_id in self.results:
            return self.results[section_id].status
        return None
    
    def get_pipeline_summary(self) -> Dict[str, Any]:
        """Get a summary of the pipeline execution"""
        
        total_sections = len(self.execution_order)
        completed = sum(1 for r in self.results.values() if r.status == SectionStatus.COMPLETED)
        failed = sum(1 for r in self.results.values() if r.status == SectionStatus.FAILED)
        retrying = sum(1 for r in self.results.values() if r.status == SectionStatus.RETRYING)
        
        avg_quality = sum(r.quality_score for r in self.results.values() if r.status == SectionStatus.COMPLETED) / max(completed, 1)
        
        return {
            "total_sections": total_sections,
            "completed": completed,
            "failed": failed,
            "retrying": retrying,
            "success_rate": completed / total_sections if total_sections > 0 else 0,
            "average_quality": avg_quality,
            "section_results": {k: {
                "status": v.status.value,
                "quality_score": v.quality_score,
                "execution_time": v.execution_time,
                "retry_count": v.retry_count
            } for k, v in self.results.items()}
        }
    
    def can_retry_section(self, section_id: str) -> bool:
        """Check if a section can be retried"""
        
        if section_id not in self.results:
            return False
        
        result = self.results[section_id]
        section = self.sections[section_id]
        
        return (result.status == SectionStatus.FAILED and 
                result.retry_count < section.max_retries)
    
    async def retry_section(self, section_id: str, context: Any) -> bool:
        """Retry a specific section"""
        
        if not self.can_retry_section(section_id):
            logger.warning(f"Cannot retry section {section_id}")
            return False
        
        logger.info(f"Retrying section {section_id}")
        
        # Reset section result
        self.results[section_id] = SectionResult(
            section_id=section_id,
            status=SectionStatus.PENDING
        )
        
        # Execute the section
        await self._execute_section_with_retries(section_id, context)
        
        return self.results[section_id].status == SectionStatus.COMPLETED 