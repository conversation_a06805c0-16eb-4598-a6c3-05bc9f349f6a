"""
Response Template Engine

Deterministic response generation with template-based routing.
"""

import logging
import re
import string
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class ResponseDepth(Enum):
    """Response depth options"""
    CASUAL = "casual"
    GENERAL = "general"
    DETAILED = "detailed"
    TECHNICAL = "technical"
    ACADEMIC = "academic"

class ResponseStyle(Enum):
    """Response style options"""
    SIMPLE = "simple"
    DETAILED = "detailed"
    TECHNICAL = "technical"
    FUNDAMENTAL = "fundamental"
    ACADEMIC = "academic"
    TRADING = "trading"
    PROFESSIONAL = "detailed"  # Alias for 'detailed' style

class ConfidenceLevel(Enum):
    """Confidence levels for recommendations"""
    VERY_LOW = (0, 20, "🔴", "Very Low")
    LOW = (21, 40, "🟠", "Low")
    MEDIUM = (41, 60, "🟡", "Medium")
    HIGH = (61, 80, "🟢", "High")
    VERY_HIGH = (81, 100, "🟢", "Very High")
    
    def __init__(self, min_val: int, max_val: int, emoji: str, label: str):
        self.min_val = min_val
        self.max_val = max_val
        self.emoji = emoji
        self.label = label
    
    @classmethod
    def from_score(cls, score: float) -> 'ConfidenceLevel':
        """Get confidence level from numeric score"""
        for level in cls:
            if level.min_val <= score <= level.max_val:
                return level
        return cls.MEDIUM

class PatternType(Enum):
    """Technical analysis pattern types"""
    HEAD_AND_SHOULDERS = "Head & Shoulders"
    INVERSE_HEAD_AND_SHOULDERS = "Inverse Head & Shoulders"
    ASCENDING_TRIANGLE = "Ascending Triangle"
    DESCENDING_TRIANGLE = "Descending Triangle"
    SYMMETRICAL_TRIANGLE = "Symmetrical Triangle"
    BULL_FLAG = "Bull Flag"
    BEAR_FLAG = "Bear Flag"
    DOUBLE_TOP = "Double Top"
    DOUBLE_BOTTOM = "Double Bottom"
    CUP_AND_HANDLE = "Cup & Handle"

@dataclass
class TechnicalPattern:
    """Technical analysis pattern detection result"""
    pattern_type: PatternType
    confidence: float
    direction: str  # "bullish", "bearish", "neutral"
    strength: str   # "weak", "moderate", "strong"
    price_targets: Dict[str, float]
    stop_loss: Optional[float]
    description: str

@dataclass
class SentimentAnalysis:
    """Market sentiment analysis result"""
    overall_sentiment: float  # -1.0 to 1.0
    sentiment_label: str      # "Very Bearish" to "Very Bullish"
    confidence: float         # 0.0 to 1.0
    news_count: int
    recent_headlines: List[str]
    market_fear_greed: Optional[float]

@dataclass
class Recommendation:
    """Trading recommendation with confidence scoring"""
    action: str  # "BUY", "SELL", "HOLD", "WAIT"
    confidence: float  # 0.0 to 100.0
    reasoning: str
    risk_level: str  # "Low", "Medium", "High"
    price_targets: Dict[str, float]
    stop_loss: Optional[float]
    technical_patterns: List[TechnicalPattern]
    sentiment: SentimentAnalysis
    data_quality: float  # 0.0 to 100.0

class ResponseTemplate:
    """Response template structure"""
    
    def __init__(self, template_type: str, style: ResponseStyle, content: str):
        self.template_type = template_type
        self.style = style
        self.content = content
    
    def __str__(self):
        return f"ResponseTemplate({self.template_type}, {self.style.value})"

class SafeDict(dict):
    """Dictionary that safely handles missing keys for string formatting"""
    def __missing__(self, key):
        return f"{{{key}}}"

class ResponseTemplateEngine:
    """Enhanced response template engine with confidence scoring and pattern recognition"""
    
    def __init__(self):
        self.templates = self._load_templates()
        self.pattern_detector = PatternDetector()
        self.sentiment_analyzer = SentimentAnalyzer()
    
    def _load_templates(self) -> Dict[str, Dict[str, str]]:
        """Load response templates"""
        return {
            "stock_analysis": {
                "simple": """**{symbol} Analysis - {confidence_emoji} {confidence_level}**

💰 **Current Price:** ${price}
📈 **Change:** {change_sign}{change}% ({change_sign}${abs_change})
📊 **Volume:** {volume}

🎯 **Recommendation:** {action} ({confidence}% confidence)
⚠️ **Risk Level:** {risk_level}

{technical_analysis}
{sentiment_summary}

{data_freshness_warning}

*Data Quality: {data_quality}% • Updated: {timestamp}*""",
                
                "detailed": """**📊 {symbol} Comprehensive Analysis**

💰 **Price Action:**
• Current: ${price}
• Change: {change_sign}{change}% ({change_sign}${abs_change})
• Volume: {volume}
• Market Cap: {market_cap_formatted}

🎯 **Trading Recommendation:**
• Action: **{action}** {confidence_emoji}
• Confidence: **{confidence}%**
• Risk Level: {risk_level}

📈 **Technical Analysis:**
{technical_analysis}

💭 **Market Sentiment:**
{sentiment_summary}

🎯 **Price Targets:**
{price_targets}

⚠️ **Risk Management:**
• Stop Loss: {stop_loss_formatted}
• Position Size: {position_size}

{data_freshness_warning}

*Analysis Quality: {data_quality}% • {timestamp}*""",
                
                "technical": """**🔬 {symbol} Technical Analysis**

📊 **Price Data:**
• Current: ${price}
• Change: {change_sign}{change}%
• Volume: {volume}

🎯 **Pattern Recognition:**
{pattern_analysis}

📈 **Technical Indicators:**
{technical_indicators}

🎯 **Recommendation:** {action} ({confidence}% confidence)
⚠️ **Risk:** {risk_level}

{data_freshness_warning}

*Technical Analysis Quality: {data_quality}%*"""
            },
            
            "technical_analysis": {
                "simple": """**📊 Technical Analysis - {symbol}**

📈 **Price Action:**
• Current: ${price}
• Change: {change_sign}{change}%
• Volume: {volume}

🎯 **Technical Indicators:**
{technical_indicators}

📊 **Pattern Analysis:**
{pattern_analysis}

*Technical Analysis Quality: {data_quality}%*""",
                
                "detailed": """**📊 Comprehensive Technical Analysis - {symbol}**

📈 **Price Action:**
• Current: ${price}
• Change: {change_sign}{change}%
• Volume: {volume}
• Market Cap: {market_cap_formatted}

🎯 **Technical Indicators:**
{technical_indicators}

📊 **Pattern Analysis:**
{pattern_analysis}

🔍 **Support/Resistance:**
• Support: ${support}
• Resistance: ${resistance}

*Technical Analysis Quality: {data_quality}%*"""
            },
            
            "market_overview": {
                "simple": """**🌍 Market Overview**

📊 **Major Indices:**
{indices_summary}

💭 **Market Sentiment:** {overall_sentiment}
🎯 **Trend:** {market_trend}

🎯 **Trading Recommendation:** {action} ({confidence}% confidence)
⚠️ **Risk Level:** {risk_level}

{data_freshness_warning}

*Updated: {timestamp}*""",
                
                "detailed": """**🌍 Comprehensive Market Overview**

📊 **Index Performance:**
{indices_detailed}

💭 **Sentiment Analysis:**
• Overall: {overall_sentiment}
• Fear/Greed Index: {fear_greed}
• News Sentiment: {news_sentiment}

📈 **Market Trends:**
{market_trends}

🎯 **Key Levels:**
{key_levels}

🎯 **Trading Recommendation:** {action} ({confidence}% confidence)
⚠️ **Risk Level:** {risk_level}

{data_freshness_warning}

*Market Data Quality: {data_quality}% • {timestamp}*"""
            },
            
            "error_fallback": """**⚠️ Analysis Unavailable - Fallback Mode**

📊 **{symbol} Basic Data:**
• Price: ${price}
• Change: {change_sign}{change}%
• Volume: {volume}

ℹ️ **Note:** Advanced analysis unavailable. Using fallback data source.
🔄 **Recommendation:** Try again later for full analysis.

*Fallback Mode • {timestamp}*"""
        }
    
    def generate_response(self,
                        template_type: str,
                        style: ResponseStyle,
                        data: Dict[str, Any],
                        query_analysis: Dict[str, Any]) -> str:
        """Generate enhanced response with confidence scoring"""
        try:
            # DIAGNOSTIC: Log entry parameters
            logger.info("=== RESPONSE GENERATION START ===")
            logger.info(f"Template type: {template_type}")
            logger.info(f"Style: {style}")
            logger.info(f"Query analysis: {query_analysis}")
            logger.info(f"Input data keys: {list(data.keys())}")
            logger.info(f"Input data sample: {str(data)[:200]}...")
            
            # Normalize inputs
            if isinstance(template_type, Enum):
                template_type = template_type.value
            
            if isinstance(style, Enum):
                style = style.value
            
            # Get base template
            if template_type not in self.templates:
                logger.warning(f"Template type '{template_type}' not found, using 'stock_analysis'")
                template_type = "stock_analysis"
            
            if style not in self.templates[template_type]:
                logger.warning(f"Style '{style}' not found in template '{template_type}', using 'simple'")
                style = ResponseStyle.SIMPLE.value
            
            template = self.templates[template_type][style]
            
            logger.info(f"Using template: {template_type}/{style}")
            logger.info(f"Template content length: {len(template)}")
            logger.info(f"Template preview: {template[:200]}...")
            
            # Enhance data with confidence scoring and pattern recognition
            enhanced_data = self._enhance_data_with_analysis(data, query_analysis)
            
            logger.info(f"Enhanced data keys: {list(enhanced_data.keys())}")
            logger.info(f"Enhanced data types: {[(k, type(v).__name__) for k, v in enhanced_data.items()]}")
            logger.info(f"Confidence emoji: {enhanced_data.get('confidence_emoji', 'NOT_SET')}")
            logger.info(f"Action: {enhanced_data.get('action', 'NOT_SET')}")
            
            # Fill template with enhanced data
            response = self._fill_template(template, enhanced_data)
            
            # Add stale data warning if applicable
            stale_warning = self._generate_stale_data_warning(enhanced_data)
            if stale_warning:
                response += stale_warning
            
            # Enhance response with fresh data formatting if this is a market overview
            if template_type == 'market_overview':
                response = self._enhance_response_with_fresh_data(response, enhanced_data)
            
            # Clean up the response to remove any raw object representations
            response = self._clean_response(response)
            
            # Validate and improve response quality
            response = self._validate_response_quality(response)
            
            logger.info(f"Generated {template_type} response with {style} style")
            logger.info(f"Response length: {len(response) if response else 0}")
            logger.info("=== RESPONSE GENERATION SUCCESS ===")
            return response
            
        except Exception as e:
            logger.error(f"Error generating response: {e}", exc_info=True)
            logger.error(f"Template type: {template_type}, Style: {style}")
            logger.error(f"Data keys: {list(data.keys())}")
            return self._generate_error_response(data)
    
    def _enhance_data_with_analysis(self, data: Dict[str, Any], query_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance data with technical analysis, sentiment, and confidence scoring"""
        enhanced = data.copy()
        
        # Validate data freshness FIRST - this is critical for preventing false recommendations
        enhanced = self._validate_data_freshness(enhanced)
        
        # Add confidence scoring
        enhanced.update(self._calculate_confidence_scores(data, query_analysis))
        
        # Generate sentiment analysis
        enhanced['sentiment'] = self.sentiment_analyzer.analyze_sentiment(enhanced)
        
        # Generate recommendation
        enhanced['recommendation'] = self._generate_recommendation(enhanced)
        
        # Extract action and risk level from recommendation
        if enhanced['recommendation']:
            enhanced['action'] = enhanced['recommendation'].action
            enhanced['risk_level'] = enhanced['recommendation'].risk_level
            enhanced['confidence'] = enhanced['recommendation'].confidence
            enhanced['confidence_level'] = ConfidenceLevel.from_score(enhanced['recommendation'].confidence).label
            enhanced['confidence_emoji'] = ConfidenceLevel.from_score(enhanced['recommendation'].confidence).emoji
        
        # Assess overall data quality
        enhanced['data_quality'] = self._assess_data_quality(enhanced)
        
        return enhanced
    
    def _calculate_confidence_scores(self, data: Dict[str, Any], query_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate confidence scores for different aspects of the analysis"""
        confidence_data = {}
        
        # Data quality confidence (based on data completeness and freshness)
        data_quality = self._assess_data_quality(data)
        confidence_data['data_quality'] = data_quality
        
        # Analysis confidence (based on data quality and query complexity)
        analysis_confidence = min(data_quality * 0.8 + 20, 100)  # Base 20% + up to 80% from data quality
        confidence_data['confidence'] = analysis_confidence
        
        # Confidence level and emoji
        confidence_level = ConfidenceLevel.from_score(analysis_confidence)
        confidence_data['confidence_level'] = confidence_level.label
        confidence_data['confidence_emoji'] = confidence_level.emoji
        
        # Risk level based on confidence with null check
        if analysis_confidence is None:
            logger.warning("analysis_confidence is None in risk level calculation, using Medium risk")
            confidence_data['risk_level'] = "Medium"
        elif analysis_confidence >= 80:
            confidence_data['risk_level'] = "Low"
        elif analysis_confidence >= 60:
            confidence_data['risk_level'] = "Medium"
        else:
            confidence_data['risk_level'] = "High"
        
        return confidence_data
    
    def _assess_data_quality(self, data: Dict[str, Any]) -> float:
        """Assess the quality of available data"""
        quality_score = 0.0
        max_score = 100.0
        
        # Check data completeness - look for fields at top level or normalized equivalents
        required_fields = ['price', 'change', 'volume']
        available_fields = 0
        
        for field in required_fields:
            if field in data and data[field] is not None:
                available_fields += 1
            elif field == 'price' and 'current_price' in data and data['current_price'] is not None:
                available_fields += 1
            elif field == 'change' and 'change_percent' in data and data['change_percent'] is not None:
                available_fields += 1
            elif field == 'volume' and 'volume_traded' in data and data['volume_traded'] is not None:
                available_fields += 1
        
        quality_score += (available_fields / len(required_fields)) * 40
        
        # Check data freshness
        if 'timestamp' in data:
            quality_score += 30  # Assume recent data
        
        # Check data source reliability
        if 'status' in data and data['status'] in ['success', 'fallback']:
            quality_score += 30
        
        return min(quality_score, max_score)
    
    def _generate_recommendation(self, enhanced_data: Dict[str, Any]) -> Recommendation:
        """Generate trading recommendation with confidence scoring"""
        # Check for suspicious data - this is critical for preventing false recommendations
        if enhanced_data.get('data_suspicious', False):
            suspicious_reasons = enhanced_data.get('suspicious_reasons', ['Unknown data quality issues'])
            logger.warning(f"Generating recommendation for suspicious data - forcing HOLD. Reasons: {suspicious_reasons}")
            return Recommendation(
                action="HOLD",
                confidence=self._calculate_dynamic_confidence(enhanced_data),  # Dynamic confidence
                reasoning=f"Data appears to be from fallback source - insufficient for reliable trading recommendations. Issues: {', '.join(suspicious_reasons)}",
                risk_level="High",
                price_targets={},
                stop_loss=None,
                technical_patterns=enhanced_data.get('technical_patterns', []),
                sentiment=enhanced_data.get('sentiment', SentimentAnalysis(0.0, "Neutral", 0.5, 0, [], None)),
                data_quality=enhanced_data.get('data_quality', 5)
            )
        
        # Check for stale data - this is critical for preventing false recommendations
        if enhanced_data.get('prices_unreliable', False):
            logger.warning("Generating recommendation for unreliable price data - forcing HOLD")
            return Recommendation(
                action="HOLD",
                confidence=self._calculate_dynamic_confidence(enhanced_data),  # Dynamic confidence
                reasoning="Data is too old for reliable trading recommendations - verify current prices first",
                risk_level="High",
                price_targets={},
                stop_loss=None,
                technical_patterns=enhanced_data.get('technical_patterns', []),
                sentiment=enhanced_data.get('sentiment', SentimentAnalysis(0.0, "Neutral", 0.5, 0, [], None)),
                data_quality=enhanced_data.get('data_quality', 10)
            )
        
        # Handle None values for price_change and confidence
        price_change = enhanced_data.get('change')
        confidence = enhanced_data.get('confidence')
        
        # Log if values are None for debugging
        if price_change is None:
            logger.warning("price_change is None in _generate_recommendation, using default 0")
            price_change = 0
        if confidence is None:
            logger.warning("confidence is None in _generate_recommendation, using default 50")
            confidence = 50
        
        # Ensure numeric types for comparisons
        try:
            price_change = float(price_change)
        except (TypeError, ValueError):
            logger.error(f"price_change could not be converted to float: {price_change}, using 0")
            price_change = 0
        
        try:
            confidence = float(confidence)
        except (TypeError, ValueError):
            logger.error(f"confidence could not be converted to float: {confidence}, using 50")
            confidence = 50
        
        # Check if data is stale (but not necessarily unreliable)
        if enhanced_data.get('data_stale', False):
            age_minutes = enhanced_data.get('data_age_minutes', 0)
            if age_minutes > 15:
                # For very stale data, force HOLD
                logger.warning(f"Data is {age_minutes} minutes old - forcing HOLD recommendation")
                return Recommendation(
                    action="HOLD",
                    confidence=self._calculate_dynamic_confidence(enhanced_data),  # Dynamic confidence
                    reasoning=f"Data is {age_minutes} minutes old - insufficient for active trading recommendations",
                    risk_level="High",
                    price_targets={},
                    stop_loss=None,
                    technical_patterns=enhanced_data.get('technical_patterns', []),
                    sentiment=enhanced_data.get('sentiment', SentimentAnalysis(0.0, "Neutral", 0.5, 0, [], None)),
                    data_quality=enhanced_data.get('data_quality', 20)
                )
            else:
                # For moderately stale data, reduce confidence
                confidence = max(20, confidence * 0.7)
                reasoning_suffix = f" - Note: Data is {age_minutes} minutes old"
        else:
            reasoning_suffix = ""
        
        # Generate recommendation based on price action and confidence
        if price_change > 2 and confidence > 60:
            action = "BUY"
        elif price_change < -2 and confidence > 60:
            action = "SELL"
        else:
            action = "HOLD"
        
        return Recommendation(
            action=action,
            confidence=confidence,
            reasoning=f"Based on {abs(price_change)}% price movement and {confidence}% confidence{reasoning_suffix}",
            risk_level=enhanced_data.get('risk_level', 'Medium'),
            price_targets={},
            stop_loss=None,
            technical_patterns=enhanced_data.get('technical_patterns', []),
            sentiment=enhanced_data.get('sentiment', SentimentAnalysis(0.0, "Neutral", 0.5, 0, [], None)),
            data_quality=enhanced_data.get('data_quality', 50)
        )
    
    def _validate_response_quality(self, response: str) -> str:
        """Validate and improve response quality"""
        if not response or len(response.strip()) < 50:
            logger.warning("Response too short, adding educational content")
            response += "\n\n�� **Educational Note:** This analysis provides basic market data. For comprehensive technical analysis, consider using additional indicators and multiple timeframes."
        
        # Ensure response ends with proper disclaimer
        if "⚠️" not in response and "RISK" not in response.upper():
            response += "\n\n⚠️ **Risk Disclosure:** This is educational content, not financial advice. Past performance doesn't guarantee future results."
        
        # Remove any duplicate sections
        lines = response.split('\n')
        unique_lines = []
        seen_sections = set()
        
        for line in lines:
            if line.strip().startswith('**') and line.strip().endswith('**'):
                section = line.strip()
                if section not in seen_sections:
                    seen_sections.add(section)
                    unique_lines.append(line)
                else:
                    continue  # Skip duplicate sections
            else:
                unique_lines.append(line)
        
        return '\n'.join(unique_lines)

    def _clean_response(self, response: str) -> str:
        """Clean up response by removing raw Python object representations"""
        import re
        
        # Remove raw Python object representations
        patterns_to_clean = [
            r'SentimentAnalysis\([^)]+\)',
            r'Recommendation\([^)]+\)',
            r'TechnicalPattern\([^)]+\)',
            r'<[^>]+>',  # Remove any remaining object representations
        ]
        
        cleaned_response = response
        for pattern in patterns_to_clean:
            cleaned_response = re.sub(pattern, 'Data analysis available', cleaned_response)
        
        # Clean up any remaining raw data
        cleaned_response = re.sub(r'None', 'Data unavailable', cleaned_response)
        cleaned_response = re.sub(r'\[\]', 'No data available', cleaned_response)
        
        return cleaned_response

    def _generate_basic_technical_analysis(self, data: Dict[str, Any]) -> str:
        """Generate basic technical analysis when detailed indicators are unavailable"""
        analysis_parts = []
        
        # Price action analysis
        if 'price' in data and 'change' in data and data['price'] and data['change']:
            try:
                price = float(data['price'])
                change = float(data['change'])
                
                if change > 2:
                    analysis_parts.append("📈 Strong upward momentum")
                elif change > 0.5:
                    analysis_parts.append("📈 Moderate upward movement")
                elif change < -2:
                    analysis_parts.append("📉 Strong downward pressure")
                elif change < -0.5:
                    analysis_parts.append("📉 Moderate downward movement")
                else:
                    analysis_parts.append("➡️ Sideways consolidation")
                    
            except (TypeError, ValueError):
                analysis_parts.append("📊 Price action analysis unavailable")
        
        # Volume analysis
        if 'volume' in data and data['volume']:
            try:
                volume = int(data['volume'])
                if volume > 1000000:  # 1M+ volume
                    analysis_parts.append("🔊 High trading volume")
                elif volume > 500000:  # 500K+ volume
                    analysis_parts.append("🔊 Moderate trading volume")
                else:
                    analysis_parts.append("🔊 Low trading volume")
            except (TypeError, ValueError):
                analysis_parts.append("📊 Volume analysis unavailable")
        
        # Support/Resistance analysis
        if 'support_levels' in data and data['support_levels'] and len(data['support_levels']) > 0:
            try:
                support = float(data['support_levels'][0])
                analysis_parts.append(f"🛡️ Key support at ${support:.2f}")
            except (TypeError, ValueError, IndexError):
                pass
                
        if 'resistance_levels' in data and data['resistance_levels'] and len(data['resistance_levels']) > 0:
            try:
                resistance = float(data['resistance_levels'][0])
                analysis_parts.append(f"🚧 Key resistance at ${resistance:.2f}")
            except (TypeError, ValueError, IndexError):
                pass
        
        # Market trend based on available data
        if 'sma_20' in data and data['sma_20'] and 'price' in data and data['price']:
            try:
                price = float(data['price'])
                sma_20 = float(data['sma_20'])
                if price > sma_20:
                    analysis_parts.append("📈 Above 20-day moving average (bullish)")
                else:
                    analysis_parts.append("📉 Below 20-day moving average (bearish)")
            except (TypeError, ValueError):
                pass
        
        # If no analysis could be generated, provide educational content
        if not analysis_parts:
            analysis_parts = [
                "📊 Technical analysis requires historical price data",
                "💡 Consider analyzing price action, volume, and support/resistance levels",
                "⚠️ Always use proper risk management regardless of analysis availability"
            ]
        
        return " • ".join(analysis_parts)

    def _fill_template(self, template: str, data: Dict[str, Any]) -> str:
        """Fill template with data using enhanced formatting"""
        try:
            # Create safe dictionary for missing keys
            safe_data = SafeDict(data)
            
            # Add cache warning information if available
            if 'cache_warning' in data and data['cache_warning']:
                cache_warning = data['cache_warning']
                cache_age = data.get('cache_age_seconds', 0)
                cache_age_minutes = cache_age / 60
                
                # Add cache warning to data freshness warning
                if 'data_freshness_warning' in safe_data:
                    safe_data['data_freshness_warning'] = f"{safe_data['data_freshness_warning']}\n\n⚠️ **Cache Notice:** {cache_warning}"
                else:
                    safe_data['data_freshness_warning'] = f"⚠️ **Cache Notice:** {cache_warning}"
                
                # Add cache age to timestamp for transparency
                if 'timestamp' in safe_data:
                    safe_data['timestamp'] = f"{safe_data['timestamp']} (cached {cache_age_minutes:.1f} min ago)"
            
            # Normalize field names to handle common variations
            if 'current_price' in data and 'price' not in data:
                safe_data['price'] = data['current_price']
            if 'change_percent' in data and 'change' not in data:
                safe_data['change'] = data['change_percent']
            if 'volume_traded' in data and 'volume' not in data:
                safe_data['volume'] = data['volume_traded']
            
            logger.info(f"Original data keys: {list(data.keys())}")
            logger.info(f"Normalized data keys: {list(safe_data.keys())}")
            logger.info(f"Original data values: {data}")
            
            # Add computed fields that don't conflict with template format specifiers
            if 'change' in data and data['change'] is not None:
                try:
                    change_val = float(data['change'])
                    safe_data['change_sign'] = "+" if change_val >= 0 else "-"
                    safe_data['abs_change'] = abs(change_val)
                except (TypeError, ValueError):
                    logger.error(f"change value could not be converted to float: {data['change']}, using default")
                    safe_data['change_sign'] = "+"
                    safe_data['abs_change'] = 0.0
            else:
                # Handle case where change is None (e.g., stale data)
                safe_data['change_sign'] = "±"
                safe_data['abs_change'] = 0.0
            
            # Pre-format conditional fields
            if 'market_cap' in data and data['market_cap']:
                try:
                    market_cap = data['market_cap']
                    if isinstance(market_cap, (int, float)) and market_cap > 0:
                        if market_cap >= 1e12:  # Trillion
                            safe_data['market_cap_formatted'] = f"${market_cap/1e12:.2f}T"
                        elif market_cap >= 1e9:  # Billion
                            safe_data['market_cap_formatted'] = f"${market_cap/1e9:.2f}B"
                        elif market_cap >= 1e6:  # Million
                            safe_data['market_cap_formatted'] = f"${market_cap/1e6:.2f}M"
                        else:
                            safe_data['market_cap_formatted'] = f"${market_cap:,.0f}"
                    else:
                        safe_data['market_cap_formatted'] = "Market cap data not available"
                except (TypeError, ValueError):
                    safe_data['market_cap_formatted'] = "Market cap data not available"
            else:
                safe_data['market_cap_formatted'] = "Market cap data not available"
            
            if 'stop_loss' in data and data['stop_loss']:
                safe_data['stop_loss_formatted'] = f"${data['stop_loss']}"
            else:
                safe_data['stop_loss_formatted'] = "Stop loss not configured"
            
            # Provide default values for missing keys
            default_values = {
                'symbol': 'Symbol not specified',
                'price': 0.00,
                'change': 0.00,
                'volume': 0,
                'confidence': 0,
                'data_quality': 0,
                'timestamp': datetime.now().isoformat(),
                'action': 'HOLD',
                'risk_level': 'Medium',
                'technical_analysis': 'Technical analysis data is currently being processed.',
                'sentiment_summary': 'Sentiment analysis is being calculated.',
                'market_cap': 0,
                'price_targets': 'Price targets are being analyzed based on current market conditions.',
                'stop_loss': None,
                'position_size': 'Recommended 1-2% of portfolio per trade',
                'pattern_analysis': 'Pattern analysis is being performed on current data.',
                'technical_indicators': 'Technical indicators are being calculated from historical data.',
                'indices_summary': 'Market indices data is being gathered.',
                'overall_sentiment': 'Neutral',
                'market_trend': 'Trend analysis is being performed.',
                'indices_detailed': 'Detailed market data is being collected.',
                'fear_greed': 'Fear and greed index is being calculated.',
                'news_sentiment': 'News sentiment analysis is being processed.',
                'market_trends': 'Market trends are being analyzed.',
                'key_levels': 'Key support and resistance levels are being identified.',
                'support': 0.0,
                'resistance': 0.0,
                'abs_change': 0.0,
                'confidence_emoji': '🟡',
                'confidence_level': 'Medium'
            }
            
            # Handle stale data - override price-related fields if data is unreliable
            if data.get('prices_unreliable', False):
                logger.warning("Prices marked as unreliable due to stale data - overriding price fields")
                default_values.update({
                    'price': 'Price data unavailable',
                    'current_price': 'Price data unavailable',
                    'change': 'Change data unavailable',
                    'change_percent': 'Change data unavailable',
                    'volume': 'Volume data unavailable',
                    'open': 'Open price unavailable',
                    'high': 'High price unavailable',
                    'low': 'Low price unavailable',
                    'close': 'Close price unavailable',
                    'price_targets': 'Price targets unavailable - data too old',
                    'support': 'Support levels unavailable',
                    'resistance': 'Resistance levels unavailable',
                    'technical_analysis': 'Technical analysis unavailable - data too old',
                    'technical_indicators': 'Technical indicators unavailable - data too old',
                    'data_freshness_warning': data.get('data_freshness_warning', '⚠️ Data freshness warning'),
                    'price_warning': data.get('price_warning', '⚠️ Price data may be outdated'),
                    'change_sign': '±',
                    'abs_change': 'Data unavailable'
                })
                
                # Override action to HOLD for stale data
                default_values['action'] = 'HOLD'
                default_values['risk_level'] = 'High'
                default_values['confidence'] = 0
                default_values['confidence_emoji'] = '🔴'
                default_values['confidence_level'] = 'Very Low'
            else:
                # Add data freshness warning if data is stale but not unreliable
                if data.get('data_stale', False):
                    default_values['data_freshness_warning'] = data.get('data_freshness_warning', '⚠️ Data is stale')
                else:
                    default_values['data_freshness_warning'] = '✅ Data is current'
            
            # Add data freshness information to template
            if 'data_freshness_warning' in data:
                default_values['data_freshness_warning'] = data['data_freshness_warning']
            if 'data_age_minutes' in data:
                default_values['data_age_minutes'] = data['data_age_minutes']
            if 'prices_unreliable' in data:
                default_values['prices_unreliable'] = data['prices_unreliable']
            
            # Generate actual technical indicators content if we have the data
            if 'rsi' in data or 'macd' in data or 'sma_20' in data or 'support_levels' in data:
                try:
                    from src.core.formatting.technical_analysis import TechnicalAnalysisFormatter
                    technical_indicators_content = TechnicalAnalysisFormatter.format_indicators_summary(data)
                    safe_data['technical_indicators'] = technical_indicators_content
                    safe_data['technical_analysis'] = technical_indicators_content  # Also set technical_analysis for templates
                    logger.info(f"Generated technical indicators: {technical_indicators_content[:100]}...")
                    logger.info(f"Data keys available: {list(data.keys())}")
                    logger.info(f"RSI value: {data.get('rsi')}")
                    logger.info(f"MACD value: {data.get('macd')}")
                    logger.info(f"SMA 20 value: {data.get('sma_20')}")
                except Exception as e:
                    logger.warning(f"Failed to format technical indicators: {e}")
                    # Generate basic technical analysis from available data
                    tech_analysis = self._generate_basic_technical_analysis(data)
                    safe_data['technical_indicators'] = tech_analysis
                    safe_data['technical_analysis'] = tech_analysis
            else:
                logger.warning(f"No technical indicator data found. Available keys: {list(data.keys())}")
                # Generate basic technical analysis from available data
                tech_analysis = self._generate_basic_technical_analysis(data)
                safe_data['technical_indicators'] = tech_analysis
                safe_data['technical_analysis'] = tech_analysis
            
            # Generate support and resistance summary if available
            if 'support_levels' in data and data['support_levels']:
                support_text = f"Support: ${data['support_levels'][0]:.2f}"
                if len(data['support_levels']) > 1:
                    support_text += f" (${data['support_levels'][1]:.2f})"
                safe_data['key_levels'] = support_text
            elif 'support' in data and data['support']:
                safe_data['key_levels'] = f"Support: ${data['support']:.2f}"
            else:
                safe_data['key_levels'] = 'Key support and resistance levels are being identified.'
            
            # Generate price targets if we have support/resistance data
            if 'resistance_levels' in data and data['resistance_levels']:
                targets = []
                if 'support_levels' in data and data['support_levels']:
                    targets.append(f"Support: ${data['support_levels'][0]:.2f}")
                targets.append(f"Resistance: ${data['resistance_levels'][0]:.2f}")
                safe_data['price_targets'] = " • ".join(targets)
            elif 'support' in data and data['support'] and data['support'] > 0:
                safe_data['price_targets'] = f"Support: ${data['support']:.2f}"
            elif 'resistance' in data and data['resistance'] and data['resistance'] > 0:
                safe_data['price_targets'] = f"Resistance: ${data['resistance']:.2f}"
            else:
                # Generate price targets based on current price and change
                if 'price' in data and 'change' in data and data['price'] and data['change']:
                    try:
                        price = float(data['price'])
                        change = float(data['change'])
                        
                        if change > 0:
                            # Bullish scenario
                            target_up = price * 1.05  # 5% upside
                            target_down = price * 0.95  # 5% downside
                            safe_data['price_targets'] = f"Upside target: ${target_up:.2f} • Downside support: ${target_down:.2f}"
                        else:
                            # Bearish scenario
                            target_down = price * 0.95  # 5% downside
                            target_up = price * 1.05  # 5% upside
                            safe_data['price_targets'] = f"Downside target: ${target_down:.2f} • Upside resistance: ${target_up:.2f}"
                    except (TypeError, ValueError):
                        safe_data['price_targets'] = 'Price targets are being analyzed based on current market conditions.'
                else:
                    safe_data['price_targets'] = 'Price targets are being analyzed based on current market conditions.'
            
            # Generate sentiment analysis if we have sentiment data - FIXED: Format properly instead of raw object
            if 'sentiment' in data and data['sentiment']:
                sentiment_obj = data['sentiment']
                if hasattr(sentiment_obj, 'sentiment_label'):
                    safe_data['sentiment_summary'] = f"Market sentiment: {sentiment_obj.sentiment_label}"
                elif hasattr(sentiment_obj, 'overall_sentiment'):
                    # Convert numeric sentiment to label
                    sentiment_val = sentiment_obj.overall_sentiment
                    if sentiment_val > 0.5:
                        label = "Bullish"
                    elif sentiment_val < -0.5:
                        label = "Bearish"
                    else:
                        label = "Neutral"
                    safe_data['sentiment_summary'] = f"Market sentiment: {label}"
                else:
                    safe_data['sentiment_summary'] = 'Market sentiment analysis based on technical indicators and price action.'
            elif 'overall_sentiment' in data and data['overall_sentiment']:
                safe_data['sentiment_summary'] = f"Overall sentiment: {data['overall_sentiment']}"
            else:
                safe_data['sentiment_summary'] = 'Market sentiment analysis based on technical indicators and price action.'
            
            # Generate pattern analysis if we have pattern data
            if 'technical_patterns' in data and data['technical_patterns']:
                patterns = []
                for pattern in data['technical_patterns']:
                    if hasattr(pattern, 'pattern_type'):
                        patterns.append(f"{pattern.pattern_type.value} ({pattern.confidence:.0f}% confidence)")
                if patterns:
                    safe_data['pattern_analysis'] = " • ".join(patterns)
                else:
                    safe_data['pattern_analysis'] = 'No clear technical patterns detected in current data.'
            else:
                # Generate basic pattern analysis from price action
                if 'price' in data and 'change' in data and data['price'] and data['change']:
                    try:
                        change = float(data['change'])
                        if abs(change) > 5:
                            safe_data['pattern_analysis'] = 'Strong momentum pattern forming - monitor for continuation or reversal'
                        elif abs(change) > 2:
                            safe_data['pattern_analysis'] = 'Moderate price movement - watch for pattern development'
                        else:
                            safe_data['pattern_analysis'] = 'Consolidation pattern - waiting for breakout confirmation'
                    except (TypeError, ValueError):
                        safe_data['pattern_analysis'] = 'Pattern analysis based on price action and technical indicators.'
                else:
                    safe_data['pattern_analysis'] = 'Pattern analysis based on price action and technical indicators.'
            
            # Generate market trend analysis
            if 'trend' in data and data['trend']:
                safe_data['market_trend'] = f"Market trend: {data['trend']}"
            elif 'sma_20' in data and 'price' in data:
                current_price = data['price']
                sma_20 = data['sma_20']
                # Handle None values for price and SMA
                if current_price is None or sma_20 is None:
                    logger.warning("current_price or sma_20 is None in market trend calculation")
                    safe_data['market_trend'] = "Market trend: Data unavailable for trend analysis"
                else:
                    try:
                        current_price_float = float(current_price)
                        sma_20_float = float(sma_20)
                        if current_price_float > sma_20_float:
                            safe_data['market_trend'] = "Market trend: Bullish (price above 20-day SMA)"
                        elif current_price_float < sma_20_float:
                            safe_data['market_trend'] = "Market trend: Bearish (price below 20-day SMA)"
                        else:
                            safe_data['market_trend'] = "Market trend: Neutral (price near 20-day SMA)"
                    except (TypeError, ValueError):
                        logger.error(f"Error converting price or SMA to float: price={current_price}, sma_20={sma_20}")
                        safe_data['market_trend'] = "Market trend: Error in trend calculation"
            else:
                # Generate trend analysis based on price change
                if 'price' in data and 'change' in data and data['price'] and data['change']:
                    try:
                        change = float(data['change'])
                        if change > 2:
                            safe_data['market_trend'] = "Market trend: Strong bullish momentum"
                        elif change > 0.5:
                            safe_data['market_trend'] = "Market trend: Moderately bullish"
                        elif change < -2:
                            safe_data['market_trend'] = "Market trend: Strong bearish pressure"
                        elif change < -0.5:
                            safe_data['market_trend'] = "Market trend: Moderately bearish"
                        else:
                            safe_data['market_trend'] = "Market trend: Sideways consolidation"
                    except (TypeError, ValueError):
                        safe_data['market_trend'] = 'Trend analysis based on moving averages and price action.'
                else:
                    safe_data['market_trend'] = 'Trend analysis based on moving averages and price action.'
            
            for key, default_val in default_values.items():
                if key not in safe_data:
                    safe_data[key] = default_val
            
            # FIXED: Ensure all fields are properly formatted for template (convert objects to strings)
            # Convert any enum objects to their string values
            for key, value in safe_data.items():
                if hasattr(value, 'value'):  # Enum objects
                    safe_data[key] = str(value.value)
                elif hasattr(value, '__str__') and not isinstance(value, (str, int, float, bool, type(None))):
                    safe_data[key] = str(value)

            # Ensure numeric fields are properly formatted for template
            numeric_fields = [
                'data_quality', 'support', 'resistance', 'price', 'change', 'volume',
                'abs_change', 'confidence', 'market_cap', 'stop_loss'
            ]
            for field in numeric_fields:
                if field in safe_data:
                    try:
                        if safe_data[field] is None:
                            safe_data[field] = 0.0
                        elif isinstance(safe_data[field], str):
                            # Try to convert string to float, fallback to 0.0
                            try:
                                safe_data[field] = float(safe_data[field])
                            except (ValueError, TypeError):
                                safe_data[field] = 0.0
                        else:
                            safe_data[field] = float(safe_data[field])
                    except (ValueError, TypeError):
                        safe_data[field] = 0.0
            
            # Validate required fields before template filling
            required_template_fields = ['symbol', 'price', 'change', 'volume', 'confidence', 'data_quality']
            missing_fields = [field for field in required_template_fields if field not in safe_data or safe_data[field] is None]
            
            if missing_fields:
                logger.warning(f"Missing required fields for template: {missing_fields}")
                logger.warning(f"Available fields: {list(safe_data.keys())}")
            
            # Fill template using string formatting
            try:
                result = template.format(**safe_data)
                logger.info(f"Template filled successfully with {len(safe_data)} data fields")
                return result
            except ValueError as e:
                # More specific error handling for format issues
                logger.error(f"Template format error: {e}")
                logger.error(f"Problematic data: {safe_data}")
                # Log all string values that might cause issues
                for key, value in safe_data.items():
                    if isinstance(value, str):
                        logger.error(f"String field: {key} = {value} (type: {type(value)})")
                raise
            
        except Exception as e:
            logger.error(f"Error filling template: {e}")
            # Return fallback response
            fallback_response = f"""**⚠️ Analysis Error**

Unable to generate detailed analysis at this time.

**Basic Data Available:**
• Symbol: {data.get('symbol', 'Unknown')}
• Price: ${data.get('price', data.get('current_price', 0))}
• Change: {data.get('change', data.get('change_percent', 0))}%

**Recommendation:** Try again later or use fallback mode.

*Error occurred during response generation: {str(e)}*"""
            
            return self._clean_response(fallback_response)
    
    def _generate_error_response(self, data: Dict[str, Any]) -> str:
        """Generate error response when template generation fails"""
        error_response = f"""**⚠️ Analysis Error**

Unable to generate detailed analysis at this time.

**Basic Data Available:**
• Symbol: {data.get('symbol', 'Unknown')}
• Price: ${data.get('price', data.get('current_price', 0))}
• Change: {data.get('change', data.get('change_percent', 0))}%

**Recommendation:** Try again later or use fallback mode.

*Error occurred during response generation*"""
        
        return self._clean_response(error_response)

    def generate_market_overview_response(self, data: Dict[str, Any], style: ResponseStyle = ResponseStyle.DETAILED) -> str:
        """
        Generate a comprehensive market overview response with multiple sections.
        
        Args:
            data (Dict[str, Any]): Market data and analysis information
            style (ResponseStyle): Response verbosity level
        
        Returns:
            str: Formatted market overview response
        """
        # Ensure we have default values for all potential keys
        indices = data.get('indices', [
            {'symbol': 'NVDA', 'price': 0, 'change_percent': 0},
            {'symbol': 'TSLA', 'price': 0, 'change_percent': 0},
            {'symbol': 'AMD', 'price': 0, 'change_percent': 0}
        ])
        
        # Format indices section
        indices_section = "📊 Major Indices:\n" + "\n".join([
            f"• {idx['symbol']}: ${idx['price']:.2f} ({'+' if idx['change_percent'] >= 0 else ''}{idx['change_percent']:.2f}%)"
            for idx in indices
        ])
        
        # Market sentiment and trend
        sentiment = data.get('sentiment', 'Neutral')
        trend = data.get('trend', 'Mixed')
        
        # Sector performance
        sectors = data.get('sectors', {
            'Tech': 'Neutral',
            'Semiconductors': 'Bullish',
            'AI': 'Strong'
        })
        
        sectors_section = "🏭 Sector Performance:\n" + "\n".join([
            f"• {sector}: {performance}"
            for sector, performance in sectors.items()
        ])
        
        # Key market insights
        insights = data.get('insights', [
            "AI stocks continue to drive market momentum",
            "Tech sector showing resilience amid economic uncertainty",
            "Semiconductor stocks remain attractive"
        ])
        
        insights_section = "💡 Market Insights:\n" + "\n".join([
            f"• {insight}" for insight in insights
        ])
        
        # Risk factors
        risk_factors = data.get('risk_factors', [
            "Fed policy uncertainty",
            "Global trade tensions",
            "Tech sector valuation concerns"
        ])
        
        risk_section = "⚠️ Risk Factors:\n" + "\n".join([
            f"• {factor}" for factor in risk_factors
        ])
        
        # Trading strategy
        strategy = data.get('strategy', {
            'focus': 'Quality tech with strong balance sheets',
            'approach': 'Defensive with selective growth exposure'
        })
        
        strategy_section = f"🎯 Trading Strategy:\n• Focus: {strategy['focus']}\n• Approach: {strategy['approach']}"
        
        # Combine all sections
        full_response = f"""🌍 Market Overview

{indices_section}

💭 Market Sentiment: {sentiment}
🎯 Market Trend: {trend}

{sectors_section}

{insights_section}

{risk_section}

{strategy_section}

*Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*"""
        
        return full_response

    def _validate_data_freshness(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data freshness and mark stale data appropriately"""
        from datetime import datetime, timedelta
        
        validated_data = data.copy()
        current_time = datetime.now()
        
        # Check if we have timestamp data
        if 'timestamp' in data and data['timestamp']:
            try:
                # Parse timestamp - handle various formats
                timestamp_str = str(data['timestamp'])
                if 'T' in timestamp_str:
                    # ISO format
                    if timestamp_str.endswith('Z'):
                        timestamp_str = timestamp_str[:-1] + '+00:00'
                    timestamp = datetime.fromisoformat(timestamp_str)
                else:
                    # Try other common formats
                    timestamp = datetime.fromisoformat(timestamp_str)
                
                # Calculate age
                age_minutes = (current_time - timestamp).total_seconds() / 60
                
                # CRITICAL: Check for suspicious data patterns that indicate fallback sources
                suspicious_patterns = []
                
                # Pattern 1: High/Low/Open are all 0 or None (common in yfinance fallback)
                if (data.get('high') == 0 or data.get('high') is None) and \
                   (data.get('low') == 0 or data.get('low') is None) and \
                   (data.get('open') == 0 or data.get('open') is None):
                    suspicious_patterns.append("Missing OHLC data (possible fallback source)")
                
                # Pattern 2: Close equals current_price exactly (suspicious in real-time data)
                if data.get('close') == data.get('current_price') and data.get('close') is not None:
                    suspicious_patterns.append("Close equals current price (possible fallback)")
                
                # Pattern 3: Provider indicates fallback
                if data.get('provider') in ['fallback', 'yfinance']:
                    suspicious_patterns.append(f"Data from fallback provider: {data.get('provider')}")
                
                # Pattern 4: Very low volume for active stocks (suspicious)
                if data.get('volume') and data.get('volume') < 1000:
                    suspicious_patterns.append("Unusually low volume (possible stale data)")
                
                # If we detect suspicious patterns, mark data as potentially unreliable
                if suspicious_patterns:
                    logger.warning(f"Suspicious data patterns detected: {suspicious_patterns}")
                    validated_data['data_suspicious'] = True
                    validated_data['suspicious_reasons'] = suspicious_patterns
                    validated_data['data_freshness_warning'] = f"⚠️ Data may be from fallback source - verify prices independently"
                    
                    # For suspicious data, be more conservative with freshness thresholds
                    if age_minutes > 2:  # Lower threshold for suspicious data
                        validated_data['data_stale'] = True
                        validated_data['data_age_minutes'] = int(age_minutes)
                        validated_data['data_freshness_warning'] = f"⚠️ Data is {int(age_minutes)} minutes old and may be from fallback source"
                        
                        if age_minutes > 5:  # Much lower threshold for suspicious data
                            validated_data['prices_unreliable'] = True
                            validated_data['price_warning'] = "⚠️ Prices likely outdated - verify before trading"
                            
                            # Clear out specific price fields to prevent false recommendations
                            for key in ['current_price', 'price', 'close', 'open', 'high', 'low']:
                                if key in validated_data:
                                    validated_data[key] = None
                                    validated_data[f"{key}_unreliable"] = True
                            
                            # Clear change percentages
                            for key in ['change', 'change_percent']:
                                if key in validated_data:
                                    validated_data[key] = None
                                    validated_data[f"{key}_unreliable"] = True
                
                # Standard freshness validation (for non-suspicious data)
                elif age_minutes > 5:
                    validated_data['data_stale'] = True
                    validated_data['data_age_minutes'] = int(age_minutes)
                    validated_data['data_freshness_warning'] = f"⚠️ Data is {int(age_minutes)} minutes old"
                    
                    # For very old data (>15 minutes), mark prices as unreliable
                    if age_minutes > 15:
                        validated_data['prices_unreliable'] = True
                        validated_data['price_warning'] = "⚠️ Prices may be outdated - verify before trading"
                        
                        # Clear out specific price fields to prevent false recommendations
                        for key in ['current_price', 'price', 'close', 'open', 'high', 'low']:
                            if key in validated_data:
                                validated_data[key] = None
                                validated_data[f"{key}_unreliable"] = True
                        
                        # Clear change percentages
                        for key in ['change', 'change_percent']:
                            if key in validated_data:
                                validated_data[key] = None
                                validated_data[f"{key}_unreliable"] = True
                        
                        # Clear volume if very old
                        if age_minutes > 60:
                            if 'volume' in validated_data:
                                validated_data['volume'] = None
                                validated_data['volume_unreliable'] = True
                
                else:
                    validated_data['data_stale'] = False
                    validated_data['data_age_minutes'] = int(age_minutes)
                    validated_data['data_freshness_warning'] = "✅ Data is current"
                    
            except (ValueError, TypeError) as e:
                logger.warning(f"Could not parse timestamp '{data.get('timestamp')}': {e}")
                validated_data['data_stale'] = True
                validated_data['data_freshness_warning'] = "⚠️ Timestamp validation failed"
                validated_data['prices_unreliable'] = True
        
        # Check individual symbol data freshness
        if 'data' in validated_data and isinstance(validated_data['data'], dict):
            for symbol, symbol_data in validated_data['data'].items():
                if isinstance(symbol_data, dict) and 'timestamp' in symbol_data:
                    try:
                        symbol_timestamp_str = str(symbol_data['timestamp'])
                        if 'T' in symbol_timestamp_str:
                            if symbol_timestamp_str.endswith('Z'):
                                symbol_timestamp_str = symbol_timestamp_str[:-1] + '+00:00'
                            symbol_timestamp = datetime.fromisoformat(symbol_timestamp_str)
                            
                            symbol_age_minutes = (current_time - symbol_timestamp).total_seconds() / 60
                            
                            # Apply same suspicious pattern detection to individual symbols
                            symbol_suspicious = []
                            if (symbol_data.get('high') == 0 or symbol_data.get('high') is None) and \
                               (symbol_data.get('low') == 0 or symbol_data.get('low') is None) and \
                               (symbol_data.get('open') == 0 or symbol_data.get('open') is None):
                                symbol_suspicious.append("Missing OHLC data")
                            
                            if symbol_data.get('close') == symbol_data.get('current_price') and symbol_data.get('close') is not None:
                                symbol_suspicious.append("Close equals current price")
                            
                            if symbol_data.get('provider') in ['fallback', 'yfinance']:
                                symbol_suspicious.append(f"Fallback provider: {symbol_data.get('provider')}")
                            
                            if symbol_suspicious:
                                validated_data['data'][symbol]['data_suspicious'] = True
                                validated_data['data'][symbol]['suspicious_reasons'] = symbol_suspicious
                                
                                # Lower thresholds for suspicious data
                                if symbol_age_minutes > 2:
                                    validated_data['data'][symbol]['data_stale'] = True
                                    validated_data['data'][symbol]['data_age_minutes'] = int(symbol_age_minutes)
                                    
                                    if symbol_age_minutes > 5:
                                        validated_data['data'][symbol]['prices_unreliable'] = True
                                        # Clear price data for stale symbols
                                        for price_key in ['current_price', 'price', 'close', 'open', 'high', 'low']:
                                            if price_key in validated_data['data'][symbol]:
                                                validated_data['data'][symbol][price_key] = None
                                        
                                        # Clear change data
                                        for change_key in ['change', 'change_percent']:
                                            if change_key in validated_data['data'][symbol]:
                                                validated_data['data'][symbol][change_key] = None
                            else:
                                # Standard validation for non-suspicious data
                                if symbol_age_minutes > 5:
                                    validated_data['data'][symbol]['data_stale'] = True
                                    validated_data['data'][symbol]['data_age_minutes'] = int(symbol_age_minutes)
                                    
                                    if symbol_age_minutes > 15:
                                        validated_data['data'][symbol]['prices_unreliable'] = True
                                        # Clear price data for stale symbols
                                        for price_key in ['current_price', 'price', 'close', 'open', 'high', 'low']:
                                            if price_key in validated_data['data'][symbol]:
                                                validated_data['data'][symbol][price_key] = None
                                        
                                        # Clear change data
                                        for change_key in ['change', 'change_percent']:
                                            if change_key in validated_data['data'][symbol]:
                                                validated_data['data'][symbol][change_key] = None
                                
                                else:
                                    validated_data['data'][symbol]['data_stale'] = False
                                    validated_data['data'][symbol]['data_age_minutes'] = int(symbol_age_minutes)
                                
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Could not parse symbol timestamp for {symbol}: {e}")
                        validated_data['data'][symbol]['data_stale'] = True
                        validated_data['data'][symbol]['prices_unreliable'] = True
        
        return validated_data

    def _generate_stale_data_warning(self, data: Dict[str, Any]) -> str:
        """Generate appropriate warning for stale data"""
        warnings = []
        
        # Check for suspicious data first
        if data.get('data_suspicious', False):
            suspicious_reasons = data.get('suspicious_reasons', ['Unknown data quality issues'])
            warnings.append(f"🚨 **CRITICAL WARNING:** Data appears to be from fallback source. Issues: {', '.join(suspicious_reasons)}")
            warnings.append("⚠️ **DO NOT** use these prices for trading decisions. Verify current data independently.")
        
        # Check for stale data
        if data.get('data_stale', False):
            age_minutes = data.get('data_age_minutes', 0)
            
            if age_minutes > 60:
                warnings.append("🚨 **CRITICAL WARNING:** Market data is over 1 hour old. **DO NOT** use these prices for trading decisions. Please refresh data for current market conditions.")
            elif age_minutes > 15:
                warnings.append("⚠️ **IMPORTANT:** Market data is over 15 minutes old. Prices may be outdated. Verify current data before making trading decisions.")
            elif age_minutes > 5:
                warnings.append("⚠️ **Note:** Market data is over 5 minutes old. Consider refreshing for most current prices.")
        
        # Check for unreliable prices
        if data.get('prices_unreliable', False):
            warnings.append("🚨 **PRICE RELIABILITY WARNING:** Prices marked as unreliable due to data age or quality issues.")
        
        if warnings:
            return "\n\n" + "\n\n".join(warnings)
        
        return ""

    def _adjust_recommendation_for_stale_data(self, recommendation: Recommendation, data: Dict[str, Any]) -> Recommendation:
        """Adjust trading recommendation based on data freshness"""
        if not data.get('data_stale', False):
            return recommendation
        
        age_minutes = data.get('data_age_minutes', 0)
        
        # For stale data, reduce confidence and change action to HOLD
        if age_minutes > 15:
            recommendation.action = "HOLD"
            recommendation.confidence = max(10, recommendation.confidence * 0.3)  # Reduce confidence significantly
            recommendation.reasoning = f"Data is {age_minutes} minutes old - insufficient for active trading recommendations"
            recommendation.risk_level = "High"
        elif age_minutes > 5:
            recommendation.confidence = max(20, recommendation.confidence * 0.7)  # Reduce confidence moderately
            recommendation.reasoning += f" - Note: Data is {age_minutes} minutes old"
        
        return recommendation

    def _format_symbol_data_with_freshness(self, symbol: str, symbol_data: Dict[str, Any]) -> str:
        """Format individual symbol data with freshness warnings"""
        if not symbol_data.get('data_available', True):
            return f"• **{symbol}**: Data unavailable"
        
        # Check if this symbol's data is stale
        if symbol_data.get('data_stale', False):
            age_minutes = symbol_data.get('data_age_minutes', 0)
            if age_minutes > 15:
                return f"• **{symbol}**: ⚠️ Data {age_minutes}min old - prices unreliable"
            else:
                return f"• **{symbol}**: ⚠️ Data {age_minutes}min old"
        
        # Format current data
        current_price = symbol_data.get('current_price')
        change_percent = symbol_data.get('change_percent', 0)
        
        if current_price is not None:
            change_sign = "+" if change_percent >= 0 else ""
            return f"• **{symbol}**: ${current_price:.2f} ({change_sign}{change_percent:.2f}%)"
        else:
            return f"• **{symbol}**: Price data unavailable"
    
    def _generate_market_overview_with_freshness(self, data: Dict[str, Any]) -> str:
        """Generate market overview with data freshness indicators"""
        if 'data' not in data or not isinstance(data['data'], dict):
            return "Market data unavailable"
        
        overview_parts = []
        
        for symbol, symbol_data in data['data'].items():
            overview_parts.append(self._format_symbol_data_with_freshness(symbol, symbol_data))
        
        return "\n".join(overview_parts)

    def _enhance_response_with_fresh_data(self, response: str, data: Dict[str, Any]) -> str:
        """Enhance the response with fresh data formatting for market overview"""
        if 'data' not in data or not isinstance(data['data'], dict):
            return response
        
        # Find the indices section in the response
        indices_match = re.search(r"📊 Major Indices:\n(.*?)(?=\n📊 Major Indices:|\n💭 Market Sentiment:|\n🎯 Market Trend:|\n🏭 Sector Performance:|\n💡 Market Insights:|\n⚠️ Risk Factors:|\n🎯 Trading Strategy:|\n*Updated:)", response, re.DOTALL)
        
        if indices_match:
            indices_section = indices_match.group(1)
            enhanced_indices_section = self._generate_market_overview_with_freshness(data)
            response = response.replace(indices_section, enhanced_indices_section)
        
        # Find the sentiment and trend section in the response
        sentiment_match = re.search(r"💭 Market Sentiment:\s*(.*?)(?=\n🎯 Market Trend:|\n🏭 Sector Performance:|\n💡 Market Insights:|\n⚠️ Risk Factors:|\n🎯 Trading Strategy:|\n*Updated:)", response, re.DOTALL)
        trend_match = re.search(r"🎯 Market Trend:\s*(.*?)(?=\n🏭 Sector Performance:|\n💡 Market Insights:|\n⚠️ Risk Factors:|\n🎯 Trading Strategy:|\n*Updated:)", response, re.DOTALL)
        
        if sentiment_match and trend_match:
            sentiment_section = sentiment_match.group(1)
            trend_section = trend_match.group(1)
            
            enhanced_sentiment_section = self._generate_market_overview_with_freshness(data)
            enhanced_trend_section = self._generate_market_overview_with_freshness(data)
            
            response = response.replace(sentiment_section, enhanced_sentiment_section)
            response = response.replace(trend_section, enhanced_trend_section)
        
        # Find the sectors section in the response
        sectors_match = re.search(r"🏭 Sector Performance:\n(.*?)(?=\n💡 Market Insights:|\n⚠️ Risk Factors:|\n🎯 Trading Strategy:|\n*Updated:)", response, re.DOTALL)
        
        if sectors_match:
            sectors_section = sectors_match.group(1)
            enhanced_sectors_section = self._generate_market_overview_with_freshness(data)
            response = response.replace(sectors_section, enhanced_sectors_section)
        
        # Find the insights section in the response
        insights_match = re.search(r"💡 Market Insights:\n(.*?)(?=\n⚠️ Risk Factors:|\n🎯 Trading Strategy:|\n*Updated:)", response, re.DOTALL)
        
        if insights_match:
            insights_section = insights_match.group(1)
            enhanced_insights_section = self._generate_market_overview_with_freshness(data)
            response = response.replace(insights_section, enhanced_insights_section)
        
        # Find the risk factors section in the response
        risk_factors_match = re.search(r"⚠️ Risk Factors:\n(.*?)(?=\n🎯 Trading Strategy:|\n*Updated:)", response, re.DOTALL)
        
        if risk_factors_match:
            risk_factors_section = risk_factors_match.group(1)
            enhanced_risk_factors_section = self._generate_market_overview_with_freshness(data)
            response = response.replace(risk_factors_section, enhanced_risk_factors_section)
        
        # Find the strategy section in the response
        strategy_match = re.search(r"🎯 Trading Strategy:\n(.*?)(?=\n*Updated:)", response, re.DOTALL)
        
        if strategy_match:
            strategy_section = strategy_match.group(1)
            enhanced_strategy_section = self._generate_market_overview_with_freshness(data)
            response = response.replace(strategy_section, enhanced_strategy_section)
        
        return response

    def _calculate_dynamic_confidence(self, enhanced_data: Dict[str, Any]) -> int:
        """Calculate confidence dynamically based on data quality, freshness, and completeness."""
        try:
            base_confidence = 50  # Start with neutral confidence
            
            # Data quality impact (0-30 points)
            data_quality = enhanced_data.get('data_quality', 0)
            quality_boost = int((data_quality / 100) * 30)
            
            # Data freshness impact (0-25 points)
            data_age_minutes = enhanced_data.get('data_age_minutes', 0)
            if data_age_minutes <= 1:
                freshness_boost = 25  # Very fresh
            elif data_age_minutes <= 5:
                freshness_boost = 20  # Fresh
            elif data_age_minutes <= 15:
                freshness_boost = 15  # Acceptable
            elif data_age_minutes <= 60:
                freshness_boost = 10  # Old
            else:
                freshness_boost = 0   # Very old
            
            # Data completeness impact (0-20 points)
            data = enhanced_data.get('data', {})
            if data:
                total_fields = 0
                available_fields = 0
                
                for symbol, stock_data in data.items():
                    required_fields = ['current_price', 'change', 'change_percent', 'volume', 'high', 'low', 'open', 'close']
                    total_fields += len(required_fields)
                    available_fields += sum(1 for field in required_fields 
                                        if stock_data.get(field) is not None and stock_data.get(field) != 0)
                
                if total_fields > 0:
                    completeness_ratio = available_fields / total_fields
                    completeness_boost = int(completeness_ratio * 20)
                else:
                    completeness_boost = 0
            else:
                completeness_boost = 0
            
            # Data reliability impact (0-25 points)
            reliability_boost = 25  # Start with full reliability
            
            if enhanced_data.get('data_suspicious', False):
                reliability_boost = 0  # Suspicious data = no reliability
            elif enhanced_data.get('prices_unreliable', False):
                reliability_boost = 5   # Unreliable prices = minimal reliability
            elif enhanced_data.get('data_stale', False):
                reliability_boost = max(10, reliability_boost - 15)  # Stale data reduces reliability
            
            # Calculate final confidence
            final_confidence = base_confidence + quality_boost + freshness_boost + completeness_boost + reliability_boost
            
            # Ensure confidence is within valid range (0-100)
            final_confidence = max(0, min(100, final_confidence))
            
            logger.debug(f"Confidence calculation: base={base_confidence}, quality={quality_boost}, "
                        f"freshness={freshness_boost}, completeness={completeness_boost}, "
                        f"reliability={reliability_boost}, final={final_confidence}")
            
            return final_confidence
            
        except Exception as e:
            logger.warning(f"Confidence calculation failed: {e}, using default 25")
            return 25

    def test_freshness_validation(self) -> bool:
        """Test method to verify freshness validation works correctly"""
        try:
            # Test with fresh data
            fresh_data = {
                'timestamp': datetime.now().isoformat(),
                'data': {
                    'TSLA': {
                        'current_price': 340.01,
                        'change_percent': 5.7,
                        'data_available': True,
                        'timestamp': datetime.now().isoformat()
                    }
                }
            }
            
            validated_fresh = self._validate_data_freshness(fresh_data)
            if validated_fresh.get('data_stale', True):
                logger.error("Fresh data incorrectly marked as stale")
                return False
            
            # Test with stale data
            stale_timestamp = (datetime.now() - timedelta(minutes=20)).isoformat()
            stale_data = {
                'timestamp': stale_timestamp,
                'data': {
                    'TSLA': {
                        'current_price': 340.01,
                        'change_percent': 5.7,
                        'data_available': True,
                        'timestamp': stale_timestamp
                    }
                }
            }
            
            validated_stale = self._validate_data_freshness(stale_data)
            if not validated_stale.get('data_stale', False):
                logger.error("Stale data incorrectly marked as fresh")
                return False
            
            if not validated_stale.get('prices_unreliable', False):
                logger.error("Stale data prices not marked as unreliable")
                return False
            
            logger.info("✅ Freshness validation test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Freshness validation test failed: {e}")
            return False

class PatternDetector:
    """Basic technical pattern detection"""
    
    def detect_patterns(self, price_data: Dict[str, Any]) -> List[TechnicalPattern]:
        """Detect basic technical patterns in price data"""
        patterns = []
        
        try:
            # Simple pattern detection logic with null checks
            if 'current_price' in price_data and 'change' in price_data:
                price = price_data['current_price']
                change = price_data['change']
                
                # Handle None values
                if price is None or change is None:
                    logger.warning("price or change is None in pattern detection, skipping pattern analysis")
                    return patterns
                
                # Convert to float safely
                try:
                    price_float = float(price)
                    change_float = float(change)
                except (TypeError, ValueError):
                    logger.error(f"Error converting price or change to float: price={price}, change={change}")
                    return patterns
                
                # Very basic pattern detection (placeholder for more sophisticated logic)
                if change_float > 5:  # Strong upward movement
                    patterns.append(TechnicalPattern(
                        pattern_type=PatternType.BULL_FLAG,
                        confidence=70.0,
                        direction="bullish",
                        strength="moderate",
                        price_targets={"resistance": price_float * 1.05},
                        stop_loss=price_float * 0.95,
                        description="Potential bull flag pattern forming"
                    ))
                elif change_float < -5:  # Strong downward movement
                    patterns.append(TechnicalPattern(
                        pattern_type=PatternType.BEAR_FLAG,
                        confidence=70.0,
                        direction="bearish",
                        strength="moderate",
                        price_targets={"support": price_float * 0.95},
                        stop_loss=price_float * 1.05,
                        description="Potential bear flag pattern forming"
                    ))
            
        except Exception as e:
            logger.warning(f"Pattern detection failed: {e}")
        
        return patterns

class SentimentAnalyzer:
    """Basic market sentiment analysis"""
    
    def analyze_sentiment(self, data: Dict[str, Any]) -> SentimentAnalysis:
        """Analyze market sentiment from available data"""
        try:
            # Simple sentiment analysis based on price movement with null check
            change = data.get('change')
            if change is None:
                logger.warning("change is None in sentiment analysis, using neutral sentiment")
                return SentimentAnalysis(0.0, "Neutral", 0.5, 0, [], None)
            
            # Convert to float safely
            try:
                change_float = float(change)
            except (TypeError, ValueError):
                logger.error(f"change value could not be converted to float: {change}, using neutral sentiment")
                return SentimentAnalysis(0.0, "Neutral", 0.5, 0, [], None)
            
            if change_float > 3:
                sentiment = 0.7  # Bullish
                label = "Bullish"
            elif change_float > 1:
                sentiment = 0.3  # Slightly Bullish
                label = "Slightly Bullish"
            elif change_float < -3:
                sentiment = -0.7  # Bearish
                label = "Bearish"
            elif change_float < -1:
                sentiment = -0.3  # Slightly Bearish
                label = "Slightly Bearish"
            else:
                sentiment = 0.0  # Neutral
                label = "Neutral"
            
            return SentimentAnalysis(
                overall_sentiment=sentiment,
                sentiment_label=label,
                confidence=0.6,  # Basic confidence for simple analysis
                news_count=0,
                recent_headlines=[],
                market_fear_greed=None
            )
            
        except Exception as e:
            logger.warning(f"Sentiment analysis failed: {e}")
            return SentimentAnalysis(0.0, "Neutral", 0.5, 0, [], None)