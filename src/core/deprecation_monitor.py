"""
Deprecation Monitoring System

Tracks usage of deprecated imports and modules to help monitor migration progress.
Provides metrics and reporting capabilities to track adoption of canonical implementations.
"""

import logging
import time
from typing import Dict, List, Set, Optional
from datetime import datetime, timedelta
import threading
import json
import os
from pathlib import Path

from src.core.logger import get_logger
from src.core.config_manager import get_config

logger = get_logger(__name__)

class DeprecationMonitor:
    """
    Monitors and tracks usage of deprecated imports and modules.
    
    Features:
    - Tracks frequency of deprecated import usage
    - Provides reporting on migration progress
    - Can emit metrics for monitoring systems
    - Periodically saves usage data to disk
    """
    
    _instance = None
    _lock = threading.RLock()
    
    @classmethod
    def get_instance(cls) -> 'DeprecationMonitor':
        """Get singleton instance of DeprecationMonitor."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = DeprecationMonitor()
            return cls._instance
    
    def __init__(self):
        """Initialize the deprecation monitor."""
        self.usage_counts: Dict[str, int] = {}
        self.last_seen: Dict[str, float] = {}
        self.callers: Dict[str, Set[str]] = {}
        self.last_save_time = time.time()
        self.save_interval = 3600  # Save every hour by default
        self.data_file = self._get_data_file_path()
        
        # Load existing data if available
        self._load_data()
        
        # Set up dedicated deprecation logger
        self.deprecation_logger = logging.getLogger("deprecation")
        self.deprecation_logger.setLevel(logging.WARNING)
        
        # Add handler for deprecation logger if not already present
        if not self.deprecation_logger.handlers:
            handler = logging.FileHandler(self._get_log_file_path())
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.deprecation_logger.addHandler(handler)
    
    def _get_data_file_path(self) -> str:
        """Get the path to the deprecation data file."""
        config = get_config()
        base_dir = config.get('logging', 'directory', '/var/log/tradingview-automation')
        
        # Create directory if it doesn't exist
        os.makedirs(base_dir, exist_ok=True)
        
        return os.path.join(base_dir, 'deprecation_usage.json')
    
    def _get_log_file_path(self) -> str:
        """Get the path to the deprecation log file."""
        config = get_config()
        base_dir = config.get('logging', 'directory', '/var/log/tradingview-automation')
        
        # Create directory if it doesn't exist
        os.makedirs(base_dir, exist_ok=True)
        
        return os.path.join(base_dir, 'deprecation.log')
    
    def _load_data(self):
        """Load deprecation usage data from disk."""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r') as f:
                    data = json.load(f)
                    self.usage_counts = data.get('usage_counts', {})
                    self.last_seen = data.get('last_seen', {})
                    
                    # Convert caller lists back to sets
                    callers_dict = data.get('callers', {})
                    for module, caller_list in callers_dict.items():
                        self.callers[module] = set(caller_list)
                    
                    logger.info(f"Loaded deprecation data for {len(self.usage_counts)} modules")
        except Exception as e:
            logger.error(f"Failed to load deprecation data: {e}")
    
    def _save_data(self):
        """Save deprecation usage data to disk."""
        try:
            # Convert callers sets to lists for JSON serialization
            callers_dict = {}
            for module, callers_set in self.callers.items():
                callers_dict[module] = list(callers_set)
            
            data = {
                'usage_counts': self.usage_counts,
                'last_seen': self.last_seen,
                'callers': callers_dict,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.data_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            self.last_save_time = time.time()
            logger.debug(f"Saved deprecation data to {self.data_file}")
        except Exception as e:
            logger.error(f"Failed to save deprecation data: {e}")
    
    def record_usage(self, module_name: str, caller_info: Optional[str] = None):
        """
        Record usage of a deprecated module.
        
        Args:
            module_name: Name of the deprecated module
            caller_info: Information about the caller (file, line, etc.)
        """
        with self._lock:
            # Update usage count
            self.usage_counts[module_name] = self.usage_counts.get(module_name, 0) + 1
            
            # Update last seen timestamp
            self.last_seen[module_name] = time.time()
            
            # Update callers set
            if caller_info:
                if module_name not in self.callers:
                    self.callers[module_name] = set()
                self.callers[module_name].add(caller_info)
            
            # Log the usage
            self.deprecation_logger.warning(
                f"Deprecated module used: {module_name}" + 
                (f" by {caller_info}" if caller_info else "")
            )
            
            # Save data periodically
            if time.time() - self.last_save_time > self.save_interval:
                self._save_data()
    
    def get_usage_report(self) -> Dict:
        """
        Get a report of deprecated module usage.
        
        Returns:
            Dict containing usage statistics
        """
        with self._lock:
            # Calculate days since last usage
            now = time.time()
            days_since_last_usage = {}
            for module, timestamp in self.last_seen.items():
                days = (now - timestamp) / (24 * 3600)
                days_since_last_usage[module] = round(days, 1)
            
            # Count unique callers
            unique_callers_count = {}
            for module, callers_set in self.callers.items():
                unique_callers_count[module] = len(callers_set)
            
            return {
                'total_modules': len(self.usage_counts),
                'total_usage_count': sum(self.usage_counts.values()),
                'modules': [
                    {
                        'name': module,
                        'usage_count': count,
                        'days_since_last_usage': days_since_last_usage.get(module, float('inf')),
                        'unique_callers': unique_callers_count.get(module, 0),
                        'callers': list(self.callers.get(module, set()))
                    }
                    for module, count in sorted(
                        self.usage_counts.items(), 
                        key=lambda x: x[1], 
                        reverse=True
                    )
                ]
            }
    
    def get_migration_progress(self) -> Dict:
        """
        Calculate migration progress based on usage patterns.
        
        Returns:
            Dict containing migration progress metrics
        """
        with self._lock:
            # Define thresholds for "migrated" modules (no usage in 30 days)
            thirty_days_ago = time.time() - (30 * 24 * 3600)
            
            # Count modules not used in the last 30 days
            migrated_modules = [
                module for module, timestamp in self.last_seen.items()
                if timestamp < thirty_days_ago
            ]
            
            # Count modules still in active use
            active_modules = [
                module for module, timestamp in self.last_seen.items()
                if timestamp >= thirty_days_ago
            ]
            
            total_modules = len(self.usage_counts)
            migrated_count = len(migrated_modules)
            
            # Calculate progress percentage
            progress_percentage = (migrated_count / total_modules * 100) if total_modules > 0 else 0
            
            return {
                'total_modules': total_modules,
                'migrated_modules_count': migrated_count,
                'active_modules_count': len(active_modules),
                'progress_percentage': round(progress_percentage, 1),
                'migrated_modules': migrated_modules,
                'active_modules': active_modules
            }


# Create a function to record deprecation that can be imported and used in backward compatibility modules
def record_deprecation(module_name: str, caller_info: Optional[str] = None):
    """
    Record usage of a deprecated module.
    
    Args:
        module_name: Name of the deprecated module
        caller_info: Information about the caller (file, line, etc.)
    """
    monitor = DeprecationMonitor.get_instance()
    monitor.record_usage(module_name, caller_info)


# Create a decorator for deprecated functions
def deprecated(alternative: str):
    """
    Decorator to mark functions as deprecated.
    
    Args:
        alternative: The recommended alternative to use
    
    Returns:
        Decorator function
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            import inspect
            caller_frame = inspect.currentframe().f_back
            caller_info = f"{caller_frame.f_code.co_filename}:{caller_frame.f_lineno}"
            
            module_name = func.__module__ + "." + func.__name__
            record_deprecation(module_name, caller_info)
            
            warnings_message = f"{module_name} is deprecated. Use {alternative} instead."
            import warnings
            warnings.warn(warnings_message, DeprecationWarning, stacklevel=2)
            
            return func(*args, **kwargs)
        
        # Copy function metadata
        import functools
        return functools.wraps(func)(wrapper)
    
    return decorator
