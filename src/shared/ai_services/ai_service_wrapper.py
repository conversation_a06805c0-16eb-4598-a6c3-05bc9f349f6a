"""
AI Service Wrapper with Correlation ID Logging

Provides correlation ID logging for all AI service calls to enable request tracing.
Enhanced with comprehensive technical analysis integration.
"""

import logging
import asyncio
import pandas as pd
import numpy as np
from typing import Any, Optional, Dict, List
from datetime import datetime, timezone

from src.core.logger import get_logger
from src.shared.technical_analysis.calculator import technical_analysis_calculator
from src.shared.technical_analysis.zones import supply_demand_detector, enhanced_zone_analyzer
from src.shared.market_analysis.unified_signal_analyzer import unified_signal_analyzer, analyze_market_data
from src.core.risk_management.atr_calculator import atr_stop_loss_calculator, StopLossConfig, TradeDirection
from src.shared.data_validation import gap_detector, assess_data_quality
from src.api.data.metrics import cache_metrics
from src.core.stale_data_detector import detect_stale_data, adjust_confidence_for_staleness
from src.core.outlier_detector import detect_outliers, adjust_confidence_for_outliers
from src.core.market_calendar import get_market_context, MarketStatus

# Import enhanced analysis modules
from src.bot.pipeline.commands.ask.stages.enhanced_analyzer import analyze_query_with_enhanced_context

logger = get_logger(__name__)


class AIChatProcessor:
    """Enhanced AI Chat Processor with comprehensive technical analysis."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize processor with technical analysis capabilities."""
        self.config = config
        self.signal_analyzer = unified_signal_analyzer
        self.technical_config = config.get('technical', {})
        
        # API call tracking for free-tier testing
        self.api_call_count = 0
        self.last_api_call_time = None
        self.max_calls_per_minute = 5  # Conservative limit for free-tier testing
        self.min_delay_between_calls = 12.0  # 12 seconds between calls (5 calls per minute)
    
    async def _check_rate_limit(self) -> bool:
        """Check if we can make an API call based on rate limiting for free-tier testing."""
        import time
        
        current_time = time.time()
        
        # Reset counter if a minute has passed
        if self.last_api_call_time is None or (current_time - self.last_api_call_time) >= 60:
            self.api_call_count = 0
            self.last_api_call_time = current_time
        
        # Check if we're within limits
        if self.api_call_count >= self.max_calls_per_minute:
            wait_time = 60 - (current_time - self.last_api_call_time)
            if wait_time > 0:
                logger.warning(f"⚠️ Rate limit reached ({self.max_calls_per_minute} calls/min). Waiting {wait_time:.1f}s...")
                await asyncio.sleep(wait_time)
                self.api_call_count = 0
                self.last_api_call_time = time.time()
        
        # Check minimum delay between calls
        if self.last_api_call_time and (current_time - self.last_api_call_time) < self.min_delay_between_calls:
            delay_needed = self.min_delay_between_calls - (current_time - self.last_api_call_time)
            logger.debug(f"⏳ Rate limiting: waiting {delay_needed:.1f}s between API calls...")
            await asyncio.sleep(delay_needed)
        
        # Increment counter and update time
        self.api_call_count += 1
        self.last_api_call_time = time.time()
        
        logger.info(f"📊 API call #{self.api_call_count}/{self.max_calls_per_minute} this minute")
        return True
    
    async def process(self, prompt: str, user_id: Optional[str] = None, guild_id: Optional[str] = None) -> Dict[str, Any]:
        """Process the AI prompt with enhanced technical analysis and context understanding."""
        try:
            # Extract symbols from prompt (basic extraction for demo)
            symbols = self._extract_symbols(prompt)
            
            # If we have user context, perform enhanced analysis
            if user_id:
                enhanced_analysis = await analyze_query_with_enhanced_context(
                    prompt, user_id, guild_id
                )
                
                # Log enhanced analysis insights
                if "integration_insights" in enhanced_analysis:
                    logger.info(f"Enhanced analysis insights: {enhanced_analysis['integration_insights']}")
            
            if symbols:
                # Fetch comprehensive technical analysis
                technical_analysis = await self._get_comprehensive_analysis(symbols[0])
                
                # Generate trading signals
                signals = await self._generate_trading_signals(symbols[0])
                
                # Generate conversational AI response
                ai_response = await self._generate_ai_chat_response(symbols[0], technical_analysis, signals)
                
                result = {
                    "response": ai_response,
                    "intent": "technical_analysis",
                    "symbol": symbols[0],
                    "technical_analysis": technical_analysis,
                    "trading_signals": signals,
                    "tools_used": ["comprehensive_technical_analysis", "signal_generation", "zone_analysis", "ai_interpretation"],
                    "response_type": "conversational_analysis"
                }
                
                # Add enhanced analysis if available
                if user_id and 'enhanced_analysis' in locals():
                    result["enhanced_context"] = enhanced_analysis
                    
                return result
            else:
                # For general questions, still provide enhanced context if available
                response = await self._generate_general_response(prompt)
                
                result = {
                    "response": response,
                    "intent": "general_question",
                    "tools_used": [],
                    "response_type": "help_message"
                }
                
                # Add enhanced analysis if available
                if user_id and 'enhanced_analysis' in locals():
                    result["enhanced_context"] = enhanced_analysis
                    
                return result
                
        except Exception as e:
            logger.error(f"Error in AI processing: {e}")
            return {
                "response": f"I apologize, but I encountered an error while processing your request: {str(e)}. Please try again or rephrase your question.",
                "intent": "error",
                "tools_used": [],
                "response_type": "error_message"
            }
    
    def _extract_symbols(self, prompt: str) -> List[str]:
        """Extract stock symbols from prompt."""
        import re

        # Look for $ followed by letters (case-insensitive) and normalize to uppercase
        symbol_pattern = r'\$([A-Za-z]+)'
        matches = re.findall(symbol_pattern, prompt)

        # Normalize to uppercase and deduplicate while preserving order
        seen = set()
        normalized = []
        for m in matches:
            up = m.upper()
            if up not in seen:
                seen.add(up)
                normalized.append(up)

        return normalized
    
    async def _get_comprehensive_analysis(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive technical analysis for a symbol."""
        try:
            # Attempt to get live market data from providers
            market_data = await self._get_live_market_data(symbol)
            
            # Assess data quality
            data_quality = self._assess_data_quality(market_data)
            
            if market_data.empty or data_quality < 50:
                # No valid data available - return error response
                logger.error(f"No valid market data available for {symbol} (quality: {data_quality:.1f}%)")
                return {
                    "symbol": symbol,
                    "error": "No valid market data available",
                    "data_quality": data_quality,
                    "recommendation": "Unable to provide analysis - data unavailable",
                    "fallback_data": False,
                    "status": "data_unavailable"
                }
            
            # Calculate all technical indicators with live data
            indicators = technical_analysis_calculator.calculate_all_indicators(
                market_data, symbol
            )
            
            # Detect supply and demand zones using enhanced analyzer
            enhanced_zones = enhanced_zone_analyzer.detect_enhanced_zones(market_data, symbol)
            
            # Calculate ATR-based stop-loss recommendations
            stop_loss_results = self._calculate_stop_loss_recommendations(
                market_data, symbol, enhanced_zones
            )
            
            # Assess data quality and detect gaps
            data_quality_assessment = await self._assess_data_quality_with_gaps(
                market_data, symbol
            )
            
            # Fetch market context (sector, industry, etc.)
            market_context = await self._get_market_context(symbol)
            
            # Generate trading signals
            signals = await self._generate_trading_signals(symbol, market_data)
            
            # Format indicators for display
            formatted_indicators = self._format_indicators(indicators)
            
            return {
                "symbol": symbol,
                "indicators": formatted_indicators,
                "zones": enhanced_zones,
                "zone_analysis": enhanced_zones.get("analysis", {}),
                "zone_recommendations": enhanced_zones.get("recommendations", []),
                "stop_loss_analysis": stop_loss_results,
                "market_context": market_context,
                "trading_signals": signals,
                "data_quality": data_quality_assessment,
                "analysis_timestamp": indicators.get("timestamp"),
                "fallback_data": False,
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Error getting technical analysis for {symbol}: {e}")
            return {"error": str(e), "status": "error"}
    
    def _format_indicators(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Format technical indicators for display."""
        # This is a stub - implement based on your specific indicator format needs
        return indicators
    
    async def _generate_trading_signals(self, symbol: str, market_data: Optional[pd.DataFrame] = None) -> List[Dict[str, Any]]:
        """Generate trading signals using the unified signal analyzer."""
        try:
            # Use provided market data or return empty if none available
            if market_data is None or market_data.empty:
                logger.warning(f"No market data available for signal generation for {symbol}")
                return []
            
            # Analyze for signals across different timeframes
            signals = []
            timeframes = ['1d', '4h', '1h']
            
            for timeframe in timeframes:
                try:
                    # Use the async analyze_market_data function from unified_signal_analyzer
                    timeframe_signals = await analyze_market_data(symbol, timeframe, market_data)
                    signals.extend(timeframe_signals)
                except Exception as e:
                    logger.warning(f"Signal analysis failed for {timeframe}: {e}")
                    continue
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating signals for {symbol}: {e}")
            return []
    
    async def _generate_ai_chat_response(self, symbol: str, technical_analysis: Dict[str, Any], signals: List[Dict[str, Any]]) -> str:
        """Generate a conversational AI response that interprets technical data and provides insights."""
        try:
            # Format the enhanced response with comprehensive analysis
            return self._format_enhanced_response(symbol, technical_analysis, signals)
        except Exception as e:
            logger.error(f"Error generating AI response for {symbol}: {e}")
            return f"I apologize, but I encountered an error while analyzing {symbol}. Please try again later."
    
    async def _generate_general_response(self, prompt: str) -> str:
        """Generate a response for general questions."""
        # This would integrate with a more sophisticated general AI model
        return f"I'd be happy to help you with your question: \"{prompt}\". Please include stock symbols (like $AAPL, $NVDA) in your question for specific stock analysis, or ask about general trading concepts and I'll do my best to assist!"
    
    def _format_enhanced_response(self, symbol: str, technical_analysis: Dict[str, Any], signals: List[Dict[str, Any]]) -> str:
        """Format the enhanced response with comprehensive analysis."""
        
        # Check if we have valid data
        if technical_analysis.get('status') == 'data_unavailable':
            return f"**❌ {symbol} Analysis Unavailable**\n\n⚠️ **No valid market data available**\n\nUnable to provide technical analysis due to data unavailability. Please try again later or verify the symbol is correct."
        
        if technical_analysis.get('status') == 'error':
            return f"**❌ {symbol} Analysis Error**\n\n⚠️ **Error occurred during analysis**\n\n{technical_analysis.get('error', 'Unknown error')}"
        
        response_parts = [f"**📊 {symbol} Comprehensive Technical Analysis**"]
        
        # Add current price and basic info
        if 'current_price' in technical_analysis.get('indicators', {}):
            price = technical_analysis['indicators']['current_price']
            response_parts.append(f"\n💰 **Current Price**: ${price:.2f}")
        else:
            response_parts.append(f"\n💰 **Current Price**: Data unavailable")
        
        # Add risk disclaimer
        response_parts.append("\n⚠️ **Disclaimer**: This analysis is for educational purposes only. Past performance doesn't guarantee future results. Always do your own research and consult with a financial advisor before making trading decisions.")
        
        return "\n".join(response_parts)
    
    async def _get_live_market_data(self, symbol: str) -> pd.DataFrame:
        """Get live market data for a symbol."""
        try:
            # This is a stub implementation - in a real system, you would call your market data provider
            # For now, we'll generate mock data for testing purposes
            return self._generate_mock_data(symbol)
        except Exception as e:
            logger.error(f"Error getting live market data for {symbol}: {e}")
            return pd.DataFrame()
    
    def _assess_data_quality(self, market_data: pd.DataFrame) -> float:
        """Assess the quality of market data."""
        if market_data is None or market_data.empty:
            return 0.0
            
        # Check for basic data quality issues
        quality_score = 100.0
        
        # Check for missing values
        missing_pct = market_data.isnull().mean().mean() * 100 if not market_data.empty else 0
        quality_score -= missing_pct
        
        # Check for sufficient data points
        if len(market_data) < 20:  # Need at least 20 data points for reliable analysis
            quality_score -= (20 - len(market_data)) * 2  # Deduct 2 points per missing data point
        
        # Ensure quality score is between 0 and 100
        return max(0.0, min(100.0, quality_score))
    
    def _calculate_stop_loss_recommendations(self, market_data: pd.DataFrame, symbol: str, zones: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate ATR-based stop-loss recommendations."""
        try:
            if market_data is None or market_data.empty:
                return {"error": "No market data available for stop-loss calculation"}
                
            # Configure stop-loss calculator
            config = StopLossConfig(
                atr_multiplier=2.0,  # Standard ATR multiplier
                risk_percentage=2.0,  # Risk per trade (% of portfolio)
                min_distance_pct=1.0,  # Minimum distance from entry (%)
                max_risk_pct=5.0  # Maximum risk per trade (%)
            )
            
            # Get current price from the most recent data point
            current_price = market_data['close'].iloc[-1] if 'close' in market_data.columns else None
            
            if current_price is None:
                return {"error": "No price data available for stop-loss calculation"}
            
            # Calculate stop-loss levels for both long and short positions
            long_stop = atr_stop_loss_calculator.calculate_atr_stop_loss(
                market_data, symbol, TradeDirection.LONG, current_price
            )
            
            short_stop = atr_stop_loss_calculator.calculate_atr_stop_loss(
                market_data, symbol, TradeDirection.SHORT, current_price
            )
            
            return {
                "long_position": {
                    "entry_price": current_price,
                    "stop_loss_price": long_stop.stop_loss_price if long_stop else None,
                    "risk_percentage": long_stop.risk_percentage if long_stop else None,
                    "position_size": long_stop.position_size if long_stop else None
                },
                "short_position": {
                    "entry_price": current_price,
                    "stop_loss_price": short_stop.stop_loss_price if short_stop else None,
                    "risk_percentage": short_stop.risk_percentage if short_stop else None,
                    "position_size": short_stop.position_size if short_stop else None
                }
            }
            
        except Exception as e:
            logger.error(f"Error calculating stop-loss recommendations: {e}")
            return {"error": str(e)}
    
    async def _get_market_context(self, symbol: str) -> Dict[str, Any]:
        """Get market context for a symbol."""
        try:
            # Get current market status
            try:
                market_status = await get_market_context(datetime.now())
            except Exception as e:
                logger.warning(f"Error getting market context: {e}")
                # Create a fallback market context
                market_status = {
                    'status': 'unknown',
                    'is_open': False,
                    'market_hours': '9:30 AM - 4:00 PM ET',
                    'next_event': 'Market open at 9:30 AM ET'
                }
            
            # If market_status is a dict (our fallback), use it directly
            if isinstance(market_status, dict):
                return {
                    "market_status": market_status.get('status', 'unknown'),
                    "market_hours": market_status.get('market_hours', 'unknown'),
                    "next_market_event": market_status.get('next_event', 'unknown'),
                    "is_market_open": market_status.get('is_open', False)
                }
            # Otherwise it's the result from get_market_context
            else:
                return {
                    "market_status": market_status.get('status', 'unknown'),
                    "market_hours": market_status.get('market_hours', '9:30 AM - 4:00 PM ET'),
                    "next_market_event": market_status.get('next_market_open', 'unknown'),
                    "is_market_open": market_status.get('is_open', False)
                }
            
        except Exception as e:
            logger.error(f"Error getting market context: {e}")
            return {"market_status": "unknown", "error": str(e)}
    
    async def _assess_data_quality_with_gaps(self, market_data: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """Assess data quality and detect gaps."""
        try:
            if market_data is None or market_data.empty:
                return {"quality_score": 0, "has_gaps": False, "gap_count": 0}
                
            # Basic quality score
            quality_score = self._assess_data_quality(market_data)
            
            # Detect gaps in the data
            gaps = gap_detector.detect_gaps(market_data, symbol) if hasattr(gap_detector, 'detect_gaps') else []
            
            # Detect stale data
            try:
                # Get the last timestamp and convert to string if available
                if not market_data.empty and len(market_data.index) > 0:
                    # Handle different index types
                    last_idx = market_data.index[-1]
                    if isinstance(last_idx, datetime):
                        last_timestamp = last_idx.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        # Convert to string for any other type
                        last_timestamp = str(last_idx)
                else:
                    last_timestamp = None
                    
                # Use a default check for staleness
                is_stale = True if last_timestamp is None else False
            except Exception as e:
                logger.warning(f"Error detecting stale data: {e}")
                is_stale = False
            
            # Detect outliers
            outliers = detect_outliers(market_data, symbol) if 'detect_outliers' in globals() else []
            
            return {
                "quality_score": quality_score,
                "has_gaps": len(gaps) > 0,
                "gap_count": len(gaps),
                "gaps": gaps,
                "is_stale": is_stale,
                "outlier_count": len(outliers),
                "outliers": outliers
            }
            
        except Exception as e:
            logger.error(f"Error assessing data quality: {e}")
            return {"quality_score": 0, "error": str(e)}
    
    def _determine_interval_type(self, interval: str) -> str:
        """Determine the interval type (daily, hourly, etc.)."""
        if interval.lower() in ['d', '1d', 'day', 'daily']:
            return 'daily'
        elif interval.lower() in ['w', '1w', 'week', 'weekly']:
            return 'weekly'
        elif interval.lower() in ['m', '1m', 'month', 'monthly']:
            return 'monthly'
        elif 'h' in interval.lower():
            return 'hourly'
        elif 'm' in interval.lower() and not 'month' in interval.lower():
            return 'minute'
        else:
            return 'unknown'
    
    def _normalize_provider_data_to_df(self, data: Dict[str, Any], symbol: str) -> pd.DataFrame:
        """Normalize provider data to a pandas DataFrame."""
        try:
            # This is a stub implementation - in a real system, you would normalize data from your provider
            # For now, we'll return an empty DataFrame
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error normalizing data for {symbol}: {e}")
            return pd.DataFrame()
    
    def _validate_ohlcv_data(self, df: pd.DataFrame) -> bool:
        """Validate OHLCV data."""
        if df is None or df.empty:
            return False
            
        # Check for required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_columns):
            return False
            
        # Check for sufficient data points
        if len(df) < 5:  # Need at least 5 data points for basic analysis
            return False
            
        return True
    
    def _generate_mock_data(self, symbol: str) -> pd.DataFrame:
        """Generate mock market data for testing purposes."""
        import numpy as np
        import pandas as pd
        from datetime import datetime, timedelta
        
        # Generate 30 days of mock data
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # Generate random price data
        np.random.seed(42)  # For reproducibility
        base_price = 100.0
        
        # Generate OHLCV data
        data = []
        current_price = base_price
        for i in range(len(dates)):
            # Random daily change (-3% to +3%)
            daily_change = np.random.uniform(-0.03, 0.03)
            open_price = current_price
            close_price = open_price * (1 + daily_change)
            
            # High and low prices
            high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.02))
            low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.02))
            
            # Volume (random between 100K and 10M)
            volume = np.random.randint(100000, 10000000)
            
            data.append({
                'date': dates[i],
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume
            })
            
            current_price = close_price
        
        # Create DataFrame
        df = pd.DataFrame(data)
        df.set_index('date', inplace=True)
        
        return df
