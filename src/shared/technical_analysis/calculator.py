"""Technical Analysis Calculator - Orchestrates indicator calculations with configurable parameters.
Supports environment-based configuration for flexible indicator settings.
"""
import logging
from typing import Dict, List, Optional, Any
import pandas as pd

from .indicators import (
    calculate_sma,
    calculate_ema,
    calculate_rsi,
    calculate_macd,
    calculate_bollinger_bands,
    calculate_atr,
    calculate_vwap,
    calculate_volume_analysis
)
from .zones import SupplyDemandDetector, supply_demand_detector
from src.core.config_manager import get_config

logger = logging.getLogger(__name__)

class TechnicalAnalysisCalculator:
    """Orchestrator for technical indicator calculations with configurable parameters.
    All parameters can be set via environment variables or constructor arguments.
    """
    
    def __init__(self, 
                 sma_window: Optional[int] = None,
                 ema_span: Optional[int] = None,
                 rsi_period: Optional[int] = None,
                 macd_fast: Optional[int] = None,
                 macd_slow: Optional[int] = None,
                 macd_signal: Optional[int] = None,
                 bb_window: Optional[int] = None,
                 bb_std: Optional[float] = None,
                 atr_period: Optional[int] = None,
                 volume_window: Optional[int] = None):
        """
        Initialize calculator with configurable parameters.
        Parameters are loaded from central configuration if not provided.
        """
        # Get central configuration
        config = get_config()
        technical_config = config.get_technical_analysis_config()
        
        # Use provided values or fall back to config
        self.sma_window = sma_window if sma_window is not None else technical_config.sma_window
        self.ema_span = ema_span if ema_span is not None else technical_config.ema_span
        self.rsi_period = rsi_period if rsi_period is not None else technical_config.rsi_period
        self.macd_fast = macd_fast if macd_fast is not None else technical_config.macd_fast
        self.macd_slow = macd_slow if macd_slow is not None else technical_config.macd_slow
        self.macd_signal = macd_signal if macd_signal is not None else technical_config.macd_signal
        self.bb_window = bb_window if bb_window is not None else technical_config.bb_window
        self.bb_std = bb_std if bb_std is not None else technical_config.bb_std
        self.atr_period = atr_period if atr_period is not None else technical_config.atr_period
        self.volume_window = volume_window if volume_window is not None else technical_config.volume_window
        
        logger.debug(f"TechnicalAnalysisCalculator initialized with: SMA={self.sma_window}, "
                    f"EMA={self.ema_span}, RSI={self.rsi_period}, MACD={self.macd_fast}/{self.macd_slow}/{self.macd_signal}, "
                    f"BB={self.bb_window}/{self.bb_std}, ATR={self.atr_period}, Volume={self.volume_window}")

    def calculate_all_indicators(self, df: pd.DataFrame, symbol: str = "") -> Dict[str, Any]:
        """
        Calculate all technical indicators for a given DataFrame.
        
        Args:
            df: Pandas DataFrame with OHLCV data (columns: open, high, low, close, volume)
            symbol: Optional symbol for logging purposes
            
        Returns:
            Dict containing all calculated indicators
        """
        if df is None or df.empty:
            logger.warning(f"Empty DataFrame provided for symbol {symbol}")
            return {}
        
        try:
            # Extract series from DataFrame
            close = df['close'] if 'close' in df.columns else None
            high = df['high'] if 'high' in df.columns else None
            low = df['low'] if 'low' in df.columns else None
            volume = df['volume'] if 'volume' in df.columns else None
            
            if close is None:
                logger.error(f"No close price data for symbol {symbol}")
                return {}
            
            results = {
                'symbol': symbol,
                'price': float(close.iloc[-1]) if not close.empty else None,
                'timestamp': pd.Timestamp.now().isoformat()
            }
            
            # Calculate individual indicators
            results.update(self._calculate_trend_indicators(close))
            results.update(self._calculate_momentum_indicators(close))
            results.update(self._calculate_volatility_indicators(close))
            
            # Calculate indicators that require multiple series
            if high is not None and low is not None:
                results.update(self._calculate_range_indicators(high, low, close))
            
            if volume is not None:
                results.update(self._calculate_volume_indicators(volume))
                if high is not None and low is not None and close is not None:
                    results.update(self._calculate_vwap_indicator(high, low, close, volume))
            
            # Calculate supply and demand zones
            zone_results = self._calculate_supply_demand_zones(df, close, high, low)
            results.update(zone_results)
            
            return results
            
        except Exception as e:
            logger.error(f"Error calculating indicators for {symbol}: {e}")
            return {'symbol': symbol, 'error': str(e)}

    def _calculate_trend_indicators(self, close: pd.Series) -> Dict[str, Any]:
        """Calculate trend-following indicators"""
        return {
            'sma': calculate_sma(close, self.sma_window),
            'ema': calculate_ema(close, self.ema_span),
            'sma_20': calculate_sma(close, 20),  # Common default
            'sma_50': calculate_sma(close, 50),  # Common default
            'ema_20': calculate_ema(close, 20),  # Common default
            'ema_50': calculate_ema(close, 50)   # Common default
        }

    def _calculate_momentum_indicators(self, close: pd.Series) -> Dict[str, Any]:
        """Calculate momentum indicators"""
        macd_result = calculate_macd(close, self.macd_fast, self.macd_slow, self.macd_signal)
        return {
            'rsi': calculate_rsi(close, self.rsi_period),
            'macd': macd_result.get('macd'),
            'macd_signal': macd_result.get('signal'),
            'macd_histogram': macd_result.get('histogram')
        }

    def _calculate_volatility_indicators(self, close: pd.Series) -> Dict[str, Any]:
        """Calculate volatility indicators"""
        bb_result = calculate_bollinger_bands(close, self.bb_window, self.bb_std)
        return {
            'bb_middle': bb_result.get('middle'),
            'bb_upper': bb_result.get('upper'),
            'bb_lower': bb_result.get('lower'),
            'bb_width': (bb_result.get('upper') - bb_result.get('lower')) / bb_result.get('middle') 
                        if bb_result.get('upper') and bb_result.get('lower') and bb_result.get('middle') else None
        }

    def _calculate_range_indicators(self, high: pd.Series, low: pd.Series, close: pd.Series) -> Dict[str, Any]:
        """Calculate range-based indicators"""
        return {
            'atr': calculate_atr(high, low, close, self.atr_period)
        }

    def _calculate_volume_indicators(self, volume: pd.Series) -> Dict[str, Any]:
        """Calculate volume analysis indicators"""
        volume_result = calculate_volume_analysis(volume, self.volume_window)
        return {
            'volume_ma': volume_result.get('volume_ma'),
            'volume_ratio': volume_result.get('volume_ratio'),
            'volume_spike': volume_result.get('volume_spike')
        }

    def _calculate_vwap_indicator(self, high: pd.Series, low: pd.Series,
                                 close: pd.Series, volume: pd.Series) -> Dict[str, Any]:
        """Calculate VWAP indicator"""
        return {
            'vwap': calculate_vwap(high, low, close, volume)
        }
    
    def _calculate_supply_demand_zones(self, df: pd.DataFrame, close: pd.Series,
                                     high: pd.Series, low: pd.Series) -> Dict[str, Any]:
        """Calculate supply and demand zones with zone analysis"""
        try:
            # Use the global zone detector - it may return a dict with a 'zones' key or a list
            raw_zones = supply_demand_detector.detect_zones(df, symbol="")

            # Normalize raw_zones to a list of zone dicts
            normalized_zones = []
            if isinstance(raw_zones, dict):
                # Expect structure: { 'zones': [ {..}, ... ], ... }
                normalized_zones = raw_zones.get('zones', []) if raw_zones.get('zones') else []
            elif isinstance(raw_zones, list):
                # Could be list of SupplyDemandZone objects or dicts
                if raw_zones and hasattr(raw_zones[0], 'to_dict'):
                    normalized_zones = [z.to_dict() for z in raw_zones]
                else:
                    normalized_zones = raw_zones
            else:
                # Fallback - attempt to coerce single zone object
                try:
                    normalized_zones = [raw_zones.to_dict()]
                except Exception:
                    normalized_zones = []

            # Calculate zone metrics using normalized list of dicts
            zone_metrics = self._calculate_zone_metrics(normalized_zones, close.iloc[-1] if not close.empty else 0)

            return {
                'supply_demand_zones': normalized_zones,
                'zone_metrics': zone_metrics
            }
        except Exception as e:
            logger.error(f"Error calculating supply/demand zones: {e}")
            return {'supply_demand_zones': [], 'zone_metrics': {}}
    
    def _calculate_zone_metrics(self, zones: List[Dict], current_price: float) -> Dict[str, Any]:
        """Calculate metrics for supply and demand zones"""
        if not zones:
            return {'total_zones': 0, 'supply_zone_count': 0, 'demand_zone_count': 0}
        
        supply_zones = [z for z in zones if z.get('zone_type') == 'supply']
        demand_zones = [z for z in zones if z.get('zone_type') == 'demand']
        
        metrics = {
            'total_zones': len(zones),
            'supply_zone_count': len(supply_zones),
            'demand_zone_count': len(demand_zones)
        }
        
        # Find nearest zones
        if supply_zones and current_price > 0:
            nearest_supply = min(supply_zones, key=lambda z: abs(z.get('center_price', 0) - current_price))
            metrics['nearest_supply'] = nearest_supply
        
        if demand_zones and current_price > 0:
            nearest_demand = min(demand_zones, key=lambda z: abs(z.get('center_price', 0) - current_price))
            metrics['nearest_demand'] = nearest_demand
        
        return metrics

    def get_configuration(self) -> Dict[str, Any]:
        """Return current configuration settings"""
        return {
            'sma_window': self.sma_window,
            'ema_span': self.ema_span,
            'rsi_period': self.rsi_period,
            'macd_fast': self.macd_fast,
            'macd_slow': self.macd_slow,
            'macd_signal': self.macd_signal,
            'bb_window': self.bb_window,
            'bb_std': self.bb_std,
            'atr_period': self.atr_period,
            'volume_window': self.volume_window
        }


# Global instance for easy access
technical_analysis_calculator = TechnicalAnalysisCalculator()