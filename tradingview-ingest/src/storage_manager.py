"""
Storage Manager for TradingView Data
Handles database and Redis operations for storing market data
"""

import asyncio
import json
import time
import os
import asyncio
import uuid
import warnings
from typing import Dict, Any, Optional, List, Union
import structlog
import redis.asyncio as redis
from prometheus_client import Counter, Histogram, Gauge

# Import configuration
from .config_dir import use_legacy_storage
from config.tradingview_config import config as app_config
from .metrics import track_legacy_storage_usage, track_legacy_storage_time
from src.shared.redis import get_redis_client, redis_manager

from .data_parser import MarketData, TechnicalIndicator, TradingSignal
from config.tradingview_config import config as tradingview_config

# Import Supabase client
from src.shared.database.supabase_base import BaseSupabaseClient

logger = structlog.get_logger()

class StorageManager:
    """Manages storage operations for market data"""
    
    def __init__(self):
        self.storage_counter = Counter(
            'tradingview_storage_operations_total',
            'Total number of storage operations',
            ['operation_type', 'data_type', 'status']
        )
        self.storage_time = Histogram(
            'tradingview_storage_operation_seconds',
            'Time spent on storage operations',
            ['operation_type', 'data_type']
        )
        self.storage_queue_size = Gauge(
            'tradingview_storage_queue_size',
            'Number of items in storage queue'
        )
        
        # Database connection - now using Supabase client
        self.supabase_client: Optional[BaseSupabaseClient] = None
        self.redis_client: Optional[redis.Redis] = None
        
        # Batch processing
        self.batch_queue: List[Union[MarketData, TechnicalIndicator, TradingSignal]] = []
        self.batch_size = tradingview_config.batch_size
        self.processing_delay = tradingview_config.processing_delay
        
        # Background task
        self.batch_processor_task: Optional[asyncio.Task] = None
    
    async def initialize(self):
        """Initialize database and Redis connections"""
        try:
            # Initialize Supabase connection
            from src.shared.database.db_manager import db_manager
            self.supabase_client = await db_manager.get_supabase_client()
            if self.supabase_client:
                logger.info("Supabase connection initialized via centralized manager")
            else:
                logger.warning("Supabase connection not available - some features will be limited")
            
            # Initialize Redis connection using centralized manager
            try:
                self.redis_client = await get_redis_client()
                if self.redis_client:
                    logger.info("Redis connection initialized via centralized manager")
                else:
                    logger.warning("Redis connection not available - some features will be limited")
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {e}")
                logger.warning("Continuing without Redis connection - some features will be limited")
                self.redis_client = None
                
            # Start batch processor
            self.batch_processor_task = asyncio.create_task(self._batch_processor())
            logger.info("Batch processor started")
            
        except Exception as e:
            logger.error("Failed to initialize storage manager", error=str(e))
            raise
    
    async def check_database_health(self) -> bool:
        """Check if database connections are healthy"""
        # If we're using Supabase, test the connection
        if self.supabase_client is None:
            return False
            
        try:
            # Simple query to test connection
            result = await self.supabase_client.query_data("webhooks", {"limit": 1})
            return result is not None
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    async def check_redis_health(self) -> bool:
        """Check if Redis connection is healthy"""
        try:
            if self.redis_client is None:
                logger.error("Redis client is None")
                return False
            await self.redis_client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return False
    
    async def ensure_connections(self):
        """Ensure all connections are healthy, reconnect if needed"""
        if not await self.check_database_health():
            logger.info("Reconnecting to database...")
            await self.initialize()
        
        if not await self.check_redis_health():
            logger.info("Reconnecting to Redis...")
            await self.initialize()
    
    async def cleanup(self):
        """Cleanup connections and tasks"""
        try:
            # Stop batch processor
            if self.batch_processor_task:
                self.batch_processor_task.cancel()
                try:
                    await self.batch_processor_task
                except asyncio.CancelledError:
                    pass
            
            # Close Redis connection
            if self.redis_client:
                await self.redis_client.close()
                
            logger.info("Storage manager cleanup completed")
            
        except Exception as e:
            logger.error("Error during storage manager cleanup", error=str(e))
    
    async def store_market_data(self, data: Union[MarketData, TechnicalIndicator, TradingSignal]):
        """
        Store market data (adds to batch queue for efficient processing)
        
        Args:
            data: Market data to store
        """
        try:
            # Store in Redis for immediate access
            await self._store_in_redis(data)
            
            # Record metrics
            self.storage_counter.labels(
                operation_type="queue_add",
                data_type=getattr(data, 'data_type', 'unknown'),
                status="success"
            ).inc()
            
            logger.info("Data stored in Redis",
                        symbol=getattr(data, 'symbol', 'unknown'),
                        data_type=getattr(data, 'data_type', 'unknown'))
            
        except Exception as e:
            self.storage_counter.labels(
                operation_type="queue_add",
                data_type=getattr(data, 'data_type', 'unknown'),
                status="error"
            ).inc()
            
            logger.error("Failed to store data", error=str(e))
            raise
    
    async def _store_in_redis(self, data: Union[MarketData, TechnicalIndicator, TradingSignal]):
        """Store data in Redis for immediate access"""
        try:
            if self.redis_client is None:
                logger.warning("Redis client is None, skipping Redis storage")
                return
                
            if isinstance(data, MarketData):
                key = f"market_data:{data.symbol}"
                value = {
                    "symbol": data.symbol,
                    "exchange": data.exchange,
                    "price": data.price,
                    "volume": data.volume,
                    "timestamp": data.timestamp,
                    "data_source": data.data_source
                }
                # Set with 1 hour expiration
                await self.redis_client.setex(key, 3600, json.dumps(value))
                
            elif isinstance(data, TechnicalIndicator):
                key = f"indicator:{data.symbol}:{data.indicator_name}"
                value = {
                    "symbol": data.symbol,
                    "indicator_name": data.indicator_name,
                    "value": data.value,
                    "timestamp": data.timestamp,
                    "parameters": data.parameters
                }
                await self.redis_client.setex(key, 3600, json.dumps(value))
                
            elif isinstance(data, TradingSignal):
                key = f"signal:{data.symbol}:{data.timestamp}"
                value = {
                    "symbol": data.symbol,
                    "signal_type": data.signal_type,
                    "confidence": data.confidence,
                    "price": data.price,
                    "timestamp": data.timestamp,
                    "indicators_used": data.indicators_used,
                    "reasoning": data.reasoning
                }
                await self.redis_client.setex(key, 3600, json.dumps(value))
                
        except Exception as e:
            logger.error("Failed to store data in Redis", error=str(e))
    
    async def _batch_processor(self):
        """Background task to process batched data"""
        while True:
            try:
                if len(self.batch_queue) >= self.batch_size:
                    # Process batch
                    batch = self.batch_queue[:self.batch_size]
                    self.batch_queue = self.batch_queue[self.batch_size:]
                    self.storage_queue_size.set(len(self.batch_queue))
                    
                    await self._process_batch(batch)
                    
                elif len(self.batch_queue) > 0:
                    # Process remaining items after delay
                    await asyncio.sleep(self.processing_delay)
                    batch = self.batch_queue.copy()
                    self.batch_queue.clear()
                    self.storage_queue_size.set(0)
                    
                    await self._process_batch(batch)
                
                else:
                    # No data to process, wait
                    await asyncio.sleep(self.processing_delay)
                    
            except asyncio.CancelledError:
                logger.info("Batch processor cancelled")
                break
            except Exception as e:
                logger.error("Error in batch processor", error=str(e))
                await asyncio.sleep(1)  # Wait before retrying
    
    async def _process_batch(self, batch: List[Union[MarketData, TechnicalIndicator, TradingSignal]]):
        """Process a batch of data items"""
        start_time = time.time()
        
        try:
            # Group by data type for efficient processing
            market_data_batch = []
            indicator_batch = []
            signal_batch = []
            
            for item in batch:
                if isinstance(item, MarketData):
                    market_data_batch.append(item)
                elif isinstance(item, TechnicalIndicator):
                    indicator_batch.append(item)
                elif isinstance(item, TradingSignal):
                    signal_batch.append(item)
            
            # Process each batch type
            tasks = []
            if market_data_batch:
                tasks.append(self._store_market_data_batch(market_data_batch))
            if indicator_batch:
                tasks.append(self._store_indicator_batch(indicator_batch))
            if signal_batch:
                tasks.append(self._store_signal_batch(signal_batch))
            
            if tasks:
                await asyncio.gather(*tasks)
            
            processing_time = time.time() - start_time
            logger.info("Batch processed successfully",
                       batch_size=len(batch),
                       processing_time_ms=round(processing_time * 1000, 2))
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error("Batch processing failed",
                        batch_size=len(batch),
                        processing_time_ms=round(processing_time * 1000, 2),
                        error=str(e))
            
            # Re-queue failed items
            self.batch_queue.extend(batch)
            self.storage_queue_size.set(len(self.batch_queue))
    
    async def _store_market_data_batch(self, batch: List[MarketData]):
        """Store batch of market data in Supabase"""
        if not batch or not self.supabase_client:
            return
        
        start_time = time.time()
        
        try:
            # Store each item in Supabase
            for data in batch:
                market_data = {
                    "symbol": data.symbol,
                    "exchange": data.exchange,
                    "price": data.price,
                    "volume": data.volume,
                    "open_price": data.open_price,
                    "high_price": data.high_price,
                    "low_price": data.low_price,
                    "close_price": data.close_price,
                    "timestamp": data.timestamp,
                    "data_source": data.data_source
                }
                
                await self.supabase_client.insert_data("market_data", market_data)
            
            processing_time = time.time() - start_time
            self.storage_time.labels(
                operation_type="batch_insert",
                data_type="market_data"
            ).observe(processing_time)
            
            self.storage_counter.labels(
                operation_type="batch_insert",
                data_type="market_data",
                status="success"
            ).inc()
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.storage_time.labels(
                operation_type="batch_insert",
                data_type="market_data"
            ).observe(processing_time)
            
            self.storage_counter.labels(
                operation_type="batch_insert",
                data_type="market_data",
                status="error"
            ).inc()
            
            logger.error("Failed to store market data batch", error=str(e))
            raise
    
    async def _store_indicator_batch(self, batch: List[TechnicalIndicator]):
        """Store batch of technical indicators in Supabase"""
        if not batch or not self.supabase_client:
            return
        
        start_time = time.time()
        
        try:
            for data in batch:
                indicator_data = {
                    "symbol": data.symbol,
                    "indicator_name": data.indicator_name,
                    "value": data.value,
                    "timestamp": data.timestamp,
                    "parameters": json.dumps(data.parameters) if data.parameters else None
                }
                
                await self.supabase_client.insert_data("technical_indicators", indicator_data)
            
            processing_time = time.time() - start_time
            self.storage_time.labels(
                operation_type="batch_insert",
                data_type="technical_indicator"
            ).observe(processing_time)
            
            self.storage_counter.labels(
                operation_type="batch_insert",
                data_type="technical_indicator",
                status="success"
            ).inc()
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.storage_time.labels(
                operation_type="batch_insert",
                data_type="technical_indicator"
            ).observe(processing_time)
            
            self.storage_counter.labels(
                operation_type="batch_insert",
                data_type="technical_indicator",
                status="error"
            ).inc()
            
            logger.error("Failed to store indicator batch", error=str(e))
            raise
    
    async def _store_signal_batch(self, batch: List[TradingSignal]):
        """Store batch of trading signals in Supabase"""
        if not batch or not self.supabase_client:
            return
        
        start_time = time.time()
        
        try:
            for data in batch:
                signal_data = {
                    "symbol": data.symbol,
                    "signal_type": data.signal_type,
                    "confidence": data.confidence,
                    "price": data.price,
                    "timestamp": data.timestamp,
                    "indicators_used": json.dumps(data.indicators_used) if data.indicators_used else None,
                    "reasoning": data.reasoning
                }
                
                await self.supabase_client.insert_data("trading_signals", signal_data)
            
            processing_time = time.time() - start_time
            self.storage_time.labels(
                operation_type="batch_insert",
                data_type="trading_signal"
            ).observe(processing_time)
            
            self.storage_counter.labels(
                operation_type="batch_insert",
                data_type="trading_signal",
                status="success"
            ).inc()
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.storage_time.labels(
                operation_type="batch_insert",
                data_type="trading_signal"
            ).observe(processing_time)
            
            self.storage_counter.labels(
                operation_type="batch_insert",
                data_type="trading_signal",
                status="error"
            ).inc()
            
            logger.error("Failed to store signal batch", error=str(e))
            raise
    
    async def _store_webhook_alert_legacy(self, webhook_data: Dict[str, Any]):
        """Legacy method to store webhook alert data in the webhook_alerts table"""
        # Emit deprecation warning
        warnings.warn(
            "The _store_webhook_alert_legacy method is deprecated and will be removed in a future version.",
            DeprecationWarning,
            stacklevel=2
        )
        
        # Log deprecation warning
        logger.warning(
            "Using deprecated legacy webhook storage method. This will be removed in a future version.",
            method="_store_webhook_alert_legacy",
            deprecation="legacy_storage"
        )
        
        # Track usage of legacy storage method
        track_legacy_storage_usage("_store_webhook_alert_legacy", "webhook_data")
        
        # Check if legacy storage is enabled via feature flag
        if not use_legacy_storage():
            logger.warning("Legacy storage methods are disabled. Using modern storage method instead.")
            return await self.store_webhook_alert(webhook_data)
            
        # Skip database operations if supabase_client is None
        if self.supabase_client is None:
            logger.info("Supabase client is None, skipping database storage")
            return True
            
        try:
            # Use context manager to track time spent in legacy storage method
            with track_legacy_storage_time("_store_webhook_alert_legacy"):
                # Log the raw webhook data for debugging
                logger.info("Processing webhook data", 
                           webhook_data=webhook_data,
                           has_raw_text='raw_text' in webhook_data,
                           has_symbol='symbol' in webhook_data,
                           data_type=type(webhook_data))
            
            # Handle different data formats intelligently
            ticker = None
            signal_type = None
            timestamp = None
            timeframe = None
            entry_price = None
            tp1_price = None
            tp2_price = None
            tp3_price = None
            sl_price = None
            raw_text = None
            
            # Format 1: Pipe-separated text (legacy format)
            if 'raw_text' in webhook_data and '|' in webhook_data['raw_text']:
                logger.info("Processing pipe-separated format")
                logger.warning(
                    "Using deprecated pipe-separated format. Please migrate to structured JSON format.",
                    format="pipe-separated",
                    deprecation="legacy_format"
                )
                # Track usage of legacy format
                track_legacy_storage_usage("pipe_separated_format", "text")
                parts = webhook_data['raw_text'].split('|')
                if len(parts) >= 9:
                    ticker = parts[0].strip()
                    signal_type = parts[1].strip()
                    timestamp = int(parts[2].strip())
                    timeframe = parts[3].strip()
                    entry_price = float(parts[4].strip())
                    tp1_price = float(parts[5].strip())
                    tp2_price = float(parts[6].strip())
                    tp3_price = float(parts[7].strip())
                    sl_price = float(parts[8].strip())
                    raw_text = webhook_data['raw_text']
                    logger.info("Successfully parsed pipe-separated format", ticker=ticker, signal_type=signal_type)
                else:
                    logger.warning(f"Invalid pipe-separated format: {webhook_data['raw_text']}")
                    return None
                    
            # Format 2: Direct field mapping (JSON array format from main.py)
            elif all(key in webhook_data for key in ['symbol', 'signal', 'timestamp', 'timeframe', 'entry_price', 'tp1_price', 'tp2_price', 'tp3_price', 'sl_price']):
                logger.info("Processing direct field format")
                ticker = webhook_data['symbol']
                signal_type = webhook_data['signal']
                timestamp = webhook_data['timestamp']
                timeframe = webhook_data['timeframe']
                entry_price = webhook_data['entry_price']
                tp1_price = webhook_data['tp1_price']
                tp2_price = webhook_data['tp2_price']
                tp3_price = webhook_data['tp3_price']
                sl_price = webhook_data['sl_price']
                raw_text = str(webhook_data)  # Convert the dict to string for storage
                logger.info("Successfully parsed direct field format", ticker=ticker, signal_type=signal_type)
                
            # Format 3: Raw text that might be JSON array
            elif 'raw_text' in webhook_data and webhook_data['raw_text'].startswith('[') and webhook_data['raw_text'].endswith(']'):
                logger.info("Processing raw JSON array text")
                logger.warning(
                    "Using deprecated raw JSON array format. Please migrate to structured JSON format.",
                    format="raw-json-array",
                    deprecation="legacy_format"
                )
                # Track usage of legacy format
                track_legacy_storage_usage("raw_json_array_format", "text")
                try:
                    import ast
                    # Safely evaluate the string representation of the array
                    array_data = ast.literal_eval(webhook_data['raw_text'])
                    if isinstance(array_data, list) and len(array_data) >= 9:
                        ticker = str(array_data[0])
                        signal_type = str(array_data[1])
                        timestamp = int(array_data[2])
                        timeframe = str(array_data[3])
                        entry_price = float(array_data[4])
                        tp1_price = float(array_data[5])
                        tp2_price = float(array_data[6])
                        tp3_price = float(array_data[7])
                        sl_price = float(array_data[8])
                        raw_text = webhook_data['raw_text']
                        logger.info("Successfully parsed raw JSON array text", ticker=ticker, signal_type=signal_type)
                    else:
                        logger.warning(f"Invalid array format in raw_text: {webhook_data['raw_text']}")
                        return None
                except Exception as e:
                    logger.error(f"Failed to parse raw JSON array text: {e}", raw_text=webhook_data['raw_text'])
                    return None
            else:
                logger.warning("Unrecognized webhook data format", 
                             webhook_data=webhook_data,
                             available_keys=list(webhook_data.keys()))
                return None
            
            # Determine alert type from signal type
            if 'ENTRY' in signal_type:
                alert_type = 'ENTRY'
            elif 'TP' in signal_type:
                alert_type = 'TP_HIT'
            elif 'SL' in signal_type:
                alert_type = 'SL_HIT'
            else:
                alert_type = 'UNKNOWN'
            
            # Insert into webhook_alerts table using Supabase
            alert_data = {
                "alert_type": alert_type,
                "signal": signal_type,
                "symbol": ticker,
                "timestamp": timestamp,
                "timeframe": timeframe,
                "entry_price": entry_price,
                "tp1_price": tp1_price,
                "tp2_price": tp2_price,
                "tp3_price": tp3_price,
                "sl_price": sl_price,
                "raw_text": raw_text
            }
            
            result = await self.supabase_client.insert_data("webhook_alerts", alert_data)
            
            if result:
                logger.info(f"Stored webhook alert", 
                           ticker=ticker, signal_type=signal_type, alert_type=alert_type)
                
                # Also store in Redis for caching with ticker as key for easy filtering
                if self.redis_client:
                    redis_key = f"alert:{ticker}:{timestamp}"
                    await self.redis_client.setex(
                        redis_key,
                        3600,  # 1 hour TTL
                        json.dumps({
                            'ticker': ticker,
                            'signal_type': signal_type,
                            'timestamp': timestamp,
                            'timeframe': timeframe,
                            'entry_price': entry_price,
                            'tp1_price': tp1_price,
                            'tp2_price': tp2_price,
                            'tp3_price': tp3_price,
                            'sl_price': sl_price,
                            'alert_type': alert_type
                        })
                    )
                    
                    # Store ticker index for easy filtering
                    await self.redis_client.sadd(f"tickers:{timeframe}", ticker)
                    await self.redis_client.zadd(f"alerts_by_time:{ticker}", {str(timestamp): timestamp})
                
                return result.get('id') if isinstance(result, dict) else None
                
        except Exception as e:
            logger.error(f"Failed to store webhook alert: {e}", 
                        webhook_data=webhook_data,
                        error_details=str(e))
            raise
                
        except Exception as e:
            logger.error(f"Failed to store webhook alert: {e}")
            raise
    
    async def store_webhook_error(self, error_message: str, webhook_data: Dict[str, Any]):
        """Store webhook error event"""
        try:
            if self.supabase_client:
                error_data = {
                    "event_type": "error",
                    "payload": json.dumps(webhook_data),
                    "status": "error",
                    "error_message": error_message
                }
                await self.supabase_client.insert_data("webhook_events", error_data)
                
        except Exception as e:
            logger.error("Failed to store webhook error", error=str(e))
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get storage manager metrics"""
        return {
            "queue_size": len(self.batch_queue),
            "total_operations": 0,  # Prometheus handles this automatically
            "storage_time_avg": 0.0  # Prometheus handles this automatically
        } 

    async def get_alerts_by_ticker(self, ticker: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get alerts for a specific ticker, ordered by time (newest first)"""
        try:
            if self.supabase_client:
                # Query webhook_alerts table using Supabase
                query_params = {
                    "symbol": f"eq.{ticker}",
                    "order": "timestamp.desc,created_at.desc",
                    "limit": str(limit)
                }
                return await self.supabase_client.query_data("webhook_alerts", query_params)
                
        except Exception as e:
            logger.error(f"Failed to get alerts for ticker {ticker}: {e}")
            raise

    async def get_alerts_by_timeframe(self, timeframe: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get alerts for a specific timeframe, ordered by time (newest first)"""
        try:
            if self.supabase_client:
                # Query webhook_alerts table using Supabase
                query_params = {
                    "timeframe": f"eq.{timeframe}",
                    "order": "timestamp.desc,created_at.desc",
                    "limit": str(limit)
                }
                return await self.supabase_client.query_data("webhook_alerts", query_params)
                
        except Exception as e:
            logger.error(f"Failed to get alerts for timeframe {timeframe}: {e}")
            raise

    async def get_alerts_by_signal_type(self, signal_type: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get alerts for a specific signal type, ordered by time (newest first)"""
        try:
            if self.supabase_client:
                # Query webhook_alerts table using Supabase
                query_params = {
                    "signal": f"eq.{signal_type}",
                    "order": "timestamp.desc,created_at.desc",
                    "limit": str(limit)
                }
                return await self.supabase_client.query_data("webhook_alerts", query_params)
                
        except Exception as e:
            logger.error(f"Failed to get alerts for signal type {signal_type}: {e}")
            raise

    async def get_recent_alerts(self, hours: int = 24, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent alerts from the last N hours, ordered by time (newest first)"""
        try:
            if self.supabase_client:
                # Calculate timestamp for N hours ago
                cutoff_time = int(time.time() - (hours * 3600))
                
                # Query webhook_alerts table using Supabase
                query_params = {
                    "created_at": f"gte.{cutoff_time}",
                    "order": "timestamp.desc,created_at.desc",
                    "limit": str(limit)
                }
                return await self.supabase_client.query_data("webhook_alerts", query_params)
                
        except Exception as e:
            logger.error(f"Failed to get recent alerts: {e}")
            raise

    async def get_ticker_summary(self, ticker: str) -> Dict[str, Any]:
        """Get a summary of all alerts for a specific ticker"""
        try:
            if self.supabase_client:
                # For now, we'll return a simplified summary
                # In a full implementation, you'd use Supabase's RPC functions or complex queries
                query_params = {
                    "symbol": f"eq.{ticker}"
                }
                alerts = await self.supabase_client.query_data("webhook_alerts", query_params)
                
                if alerts:
                    entry_alerts = [a for a in alerts if a.get('alert_type') == 'ENTRY']
                    tp_alerts = [a for a in alerts if a.get('alert_type') == 'TP_HIT']
                    sl_alerts = [a for a in alerts if a.get('alert_type') == 'SL_HIT']
                    
                    return {
                        "symbol": ticker,
                        "total_alerts": len(alerts),
                        "entry_alerts": len(entry_alerts),
                        "tp_alerts": len(tp_alerts),
                        "sl_alerts": len(sl_alerts),
                        "first_alert": min(a.get('timestamp', 0) for a in alerts) if alerts else None,
                        "last_alert": max(a.get('timestamp', 0) for a in alerts) if alerts else None
                    }
                return {}
                
        except Exception as e:
            logger.error(f"Failed to get ticker summary for {ticker}: {e}")
            raise 

    # ============================================================================
    # TICKER MANAGEMENT METHODS
    # ============================================================================
    
    async def ensure_ticker_exists(self, ticker: str) -> bool:
        """
        Ensure a ticker exists in the tickers table.
        If it doesn't exist, create it. If it exists, update last_seen.
        Returns True if ticker was created, False if it already existed.
        """
        try:
            # Check if Supabase is connected
            if not self.supabase_client:
                logger.debug("Supabase not connected, skipping ticker creation")
                return True
                
            # Check if ticker exists
            query_params = {
                "symbol": f"eq.{ticker}"
            }
            existing = await self.supabase_client.query_data("tickers", query_params)
            
            if existing and len(existing) > 0:
                # Update last_seen and increment alert_count
                update_data = {
                    "last_seen": "now()",
                    "alert_count": existing[0].get('alert_count', 0) + 1,
                    "is_active": True
                }
                
                await self.supabase_client.update_data(
                    "tickers", 
                    {"symbol": ticker}, 
                    update_data
                )
                
                logger.info(f"Updated existing ticker: {ticker} (total alerts: {existing[0].get('alert_count', 0) + 1})")
                return False  # Already existed
            else:
                # Create new ticker
                ticker_data = {
                    "symbol": ticker,
                    "first_seen": "now()",
                    "last_seen": "now()",
                    "alert_count": 1,
                    "is_active": True
                }
                
                await self.supabase_client.insert_data("tickers", ticker_data)
                logger.info(f"Created new ticker: {ticker}")
                return True  # Was created
                
        except Exception as e:
            logger.error(f"Failed to ensure ticker exists for {ticker}: {e}")
            raise
    
    async def get_all_tickers(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """Get all tickers, optionally filtered by active status"""
        try:
            if self.supabase_client:
                query_params = {}
                if active_only:
                    query_params["is_active"] = "eq.true"
                query_params["order"] = "last_seen.desc"
                
                return await self.supabase_client.query_data("tickers", query_params)
                
        except Exception as e:
            logger.error(f"Failed to get tickers: {e}")
            raise
    
    async def deactivate_ticker(self, ticker: str) -> bool:
        """Deactivate a ticker (mark as inactive)"""
        try:
            if self.supabase_client:
                update_data = {"is_active": False}
                result = await self.supabase_client.update_data(
                    "tickers", 
                    {"symbol": ticker}, 
                    update_data
                )
                
                if result:
                    logger.info(f"Deactivated ticker: {ticker}")
                    return True
                else:
                    logger.warning(f"Ticker not found for deactivation: {ticker}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to deactivate ticker {ticker}: {e}")
            raise
    
    async def reactivate_ticker(self, ticker: str) -> bool:
        """Reactivate a ticker (mark as active)"""
        try:
            if self.supabase_client:
                update_data = {"is_active": True}
                result = await self.supabase_client.update_data(
                    "tickers", 
                    {"symbol": ticker}, 
                    update_data
                )
                
                if result:
                    logger.info(f"Reactivated ticker: {ticker}")
                    return True
                else:
                    logger.warning(f"Ticker not found for reactivation: {ticker}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to reactivate ticker {ticker}: {e}")
            raise
    
    async def save_raw_webhook(self, webhook_id: str, webhook_data: Dict[str, Any]) -> bool:
        """Save raw webhook data for research purposes with retry logic"""
        return await self._execute_with_retry(
            self._save_raw_webhook_impl, webhook_id, webhook_data
        )
    
    async def _save_raw_webhook_impl(self, webhook_id: str, webhook_data: Dict[str, Any]) -> bool:
        """Implementation of raw webhook storage"""
        try:
            if not self.supabase_client:
                logger.debug("Supabase not connected, skipping local storage")
                return True
                
            # Use the existing webhooks table
            webhook_record = {
                "webhook_id": webhook_id,
                "raw_data": json.dumps(webhook_data),
                "client_ip": webhook_data.get("client_ip", "unknown"),
                "timestamp": webhook_data.get("timestamp", time.time()),
                "status": "received"
            }
            
            await self.supabase_client.insert_data("webhooks", webhook_record)
            
            logger.info("Raw webhook saved", webhook_id=webhook_id)
            return True
            
        except Exception as e:
            logger.error(f"Failed to save raw webhook {webhook_id}: {str(e)}")
            return False
    
    async def store_webhook_alert(self, alert_data: Dict[str, Any]) -> bool:
        """Store parsed webhook alert data with retry logic"""
        return await self._execute_with_retry(
            self._store_webhook_alert_impl, alert_data
        )
    
    async def _store_webhook_alert_impl(self, alert_data: Dict[str, Any]) -> bool:
        """Implementation of webhook alert storage"""
        try:
            if not self.supabase_client:
                logger.debug("Supabase not connected, skipping local storage")
                return True
            
            # Extract the actual data from the webhook structure
            # Test webhooks have: {"payload": {...}, "raw_text": "..."}
            actual_data = alert_data.get("payload", alert_data)
            
            # Use the webhooks table we created
            symbol = actual_data.get("symbol", "unknown")
            signal_type = actual_data.get("signal_type", "alert")
            timestamp = int(actual_data.get("timestamp", time.time()))
            price = actual_data.get("price")
            
            # Generate a webhook ID if not present
            webhook_id = alert_data.get("webhook_id", f"{int(time.time())}_{symbol}")
            client_ip = alert_data.get("client_ip", "unknown")
            
            webhook_record = {
                "webhook_id": webhook_id,
                "timestamp": timestamp,
                "client_ip": client_ip,
                "raw_data": json.dumps(alert_data),
                "status": "processed"
            }
            
            await self.supabase_client.insert_data("webhooks", webhook_record)
            
            logger.info("Webhook alert stored successfully", 
                       symbol=symbol,
                       alert_type=signal_type,
                       timestamp=timestamp)
            return True
            
        except Exception as e:
            logger.error("Failed to store webhook alert", 
                        error=str(e),
                        alert_data_keys=list(alert_data.keys()) if isinstance(alert_data, dict) else "not_dict",
                        symbol=alert_data.get("symbol", "unknown") if isinstance(alert_data, dict) else "unknown")
            return False
    
    async def save_parsed_data(self, webhook_id: str, parsed_data: Dict[str, Any]) -> bool:
        """Save parsed webhook data to database"""
        try:
            # Check if Supabase is connected
            if not self.supabase_client:
                logger.debug("Supabase not connected, skipping local storage")
                return True
                
            parsed_record = {
                "webhook_id": webhook_id,
                "parsed_data": json.dumps(parsed_data),
                "timestamp": time.time()
            }
            
            # Use upsert to handle conflicts
            await self.supabase_client.insert_data("parsed_webhooks", parsed_record)
            
            logger.info(f"Parsed webhook data saved: {webhook_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save parsed data for webhook {webhook_id}: {e}")
            return False
    
    async def get_ticker_stats(self) -> Dict[str, Any]:
        """Get overall ticker statistics"""
        try:
            if self.supabase_client:
                # For now, we'll return a simplified stats object
                # In a full implementation, you'd use Supabase's RPC functions or complex queries
                tickers = await self.supabase_client.query_data("tickers", {})
                
                if tickers:
                    active_tickers = [t for t in tickers if t.get('is_active', False)]
                    inactive_tickers = [t for t in tickers if not t.get('is_active', True)]
                    total_alerts = sum(t.get('alert_count', 0) for t in tickers)
                    
                    return {
                        "total_tickers": len(tickers),
                        "active_tickers": len(active_tickers),
                        "inactive_tickers": len(inactive_tickers),
                        "total_alerts": total_alerts,
                        "avg_alerts_per_ticker": total_alerts / len(tickers) if tickers else 0
                    }
                
                return {}
                
        except Exception as e:
            logger.error(f"Failed to get ticker stats: {e}")
            raise 

    async def get_health_status(self) -> Dict[str, Any]:
        """Comprehensive health check"""
        return {
            "database": await self.check_database_health(),
            "redis": await self.check_redis_health(),
            "queue_size": len(self.batch_queue),
            "queue_health": len(self.batch_queue) < 1000,  # Simple threshold
            "processor_running": self.batch_processor_task and not self.batch_processor_task.done(),
            "last_processed": getattr(self, 'last_batch_processed_time', None)
        } 

    async def _execute_with_retry(self, func, *args, max_retries: int = 3, **kwargs):
        """Execute function with exponential backoff retry"""
        for attempt in range(max_retries):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"Operation failed after {max_retries} attempts", error=str(e))
                    raise
                
                wait_time = 2 ** attempt  # Exponential backoff
                logger.warning(f"Operation attempt {attempt + 1} failed, retrying in {wait_time}s", error=str(e))
                await asyncio.sleep(wait_time)
                
                # Try to reconnect if it's a connection issue
                if attempt == 1:  # On second attempt
                    await self.ensure_connections() 